<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class BackupEnvFile extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup-env';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup .env file to backup server';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        config([
            'backup.backup.source.files.include' => ['.env'],
            'backup.backup.destination.disks' => ['log_storage'],
        ]);

        Artisan::call('backup:run --only-files');
        return 0;
    }
}
