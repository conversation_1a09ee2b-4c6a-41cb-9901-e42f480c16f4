<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class BackupLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup-logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $files = collect(Storage::disk('logs')->allFiles())
            ->filter(function ($file) {
                return Storage::disk('logs')
                    ->lastModified($file) < now()->subdays(3)->endOfDay()->timestamp;
            })
            ->filter(function ($file) {
                return Str::startsWith($file, 'laravel-') || Str::startsWith($file, 'orders');
            })
            ->map(function ($file) {
                return 'storage/logs/' . $file;
            })
            ->values();

        config([
            'backup.backup.source.files.include' => $files,
            'backup.backup.destination.disks' => ['log_storage'],
        ]);

        $status = Artisan::call('backup:run --only-files');
        if ($status == 0) {
            collect($files)->each(function ($file) {
                $file = Str::replaceFirst('storage/', '', $file);
                // Storage::delete(storage_path($file));
                unlink(storage_path($file));
            });
        }

        return 0;
    }
}
