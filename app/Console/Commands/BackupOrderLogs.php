<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class BackupLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup-order-logs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $files = collect(Storage::disk('logs')->allFiles())
            ->filter(function ($file) {
				$modeified = Storage::disk('logs')->lastModified($file);
                return $modeified > now()->subday()->startOfDay()->timestamp
					&& $modeified < now()->subday()->endOfDay()->timestamp;
            })
            ->filter(function ($file) {
                return Str::startsWith($file, 'orders');
            })
            ->each(function ($file) {
				Storage::disk('order_log_storage')->put(
					Str::remove('orders/', $file),
					Storage::disk('logs')->get($file),
				);
            });
        return 0;
    }
}
