<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class Heartbeat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'heartbeat';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // make sure scheduler is runner
        // Http::get(config('services.uptime_robot.heartbeat_url'));

        // make sure the queue is runner
        dispatch(function () {
            \Illuminate\Support\Facades\Http::get(env('UPTIME_URL'));
        })->onQueue('sc');

        return 0;
    }
}
