<?php

namespace App\Console\Commands;

use App\Models\FailedApiRequest;
use Illuminate\Console\Command;

class SendIssuesEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send-issues-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        FailedApiRequest::SendActiveIssuesEmail();
        return 0;
    }
}
