<?php

namespace App\Console\Commands;

use App\Models\Shipment;
use App\Models\ShippingStatistics;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class UpdateStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-statistics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $shipments = Shipment::whereDate('created_at', today()->toDateString())->select(['skus'])->distinct()->get()->unique('skus');

        $shipments->each(function ($shipment) {
            $skus = $shipment->skus;
            dispatch(function () use ($skus) {
                $group = Shipment::where('skus', $skus)->get();

                // to get weight average
                // $total = $group->count();

                $sortedByWeight = $group->groupBy(function ($shipment) {
                    return (string)$shipment['weight'];
                })->sortDesc();

                // $highest = $sortedByWeight->first()->count();

                // $percent = $highest / $total;

                // if ($percent >= .60 && $highest >= 4) {
                ShippingStatistics::updateOrCreate(['skus' => $skus], ['weight' => $sortedByWeight->keys()->first()]);
                // }


                //to get box_id average
                $sortedByBoxes = $group->groupBy(function ($shipment) {
                    return  $shipment['box_id'];
                })->sortDesc();

                $highest = $sortedByBoxes->first()->count();

                // $percent = $highest / $total;

                if ($highest >= 4) {
                    // if ($percent >= .60 && $highest >= 4) {
                    ShippingStatistics::updateOrCreate(['skus' => $skus], ['box_id' => $sortedByBoxes->keys()->first()]);
                }
            });
        });

        // whole thing is in ShippingStatistics::boot
        //to get dimentions average
        // $groups->each(function ($group, $index) {
        //     $total = $group->count();

        //     $sorted = $group->groupBy(function ($shipment) {
        //         return (string)$shipment['length'] . '_' . $shipment['height'] . '_' . $shipment['width'];
        //     })->sortDesc();

        //     $highest = $sorted->first()->count();

        //     $percent = $highest / $total;

        //     if ($percent >= .60 && $highest >= 10) {
        //         $dims = Str::of($sorted->keys()->first())->split('(_)');
        //         ShippingStatistics::updateOrCreate(['skus' => $index], [
        //             'length' => (float) $dims[0],
        //             'height' => (float)$dims[1],
        //             'width' => (float)$dims[2],
        //         ]);
        //     }
        // });


        return 0;
    }
}
