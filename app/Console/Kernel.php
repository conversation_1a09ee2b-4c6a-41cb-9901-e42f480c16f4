<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Http\Livewire\Settings;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->call(function () {
            $settings = new Settings;
            $settings->importCarriers();
        })
            ->environments(['production'])
            ->daily();
        // ->days([0, 1, 2, 3, 4, 5])
        // ->hourly();

        $schedule->call(function () {
            $settings = new Settings;
            $settings->voidedShipments();
        })
            ->environments(['production'])
            ->everySixHours();

        $schedule
            ->command('send-issues-email')
            // monday through thursday
            ->days([1, 2, 3, 4])
            ->at('17:30')
            ->environments(['production'])
            ->onFailure(function () {
                \Log::channel('slack')->critical(config('app.name') . ' send daily issue email failed');
            });


        $schedule
            ->command('send-issues-email')
            ->fridays()
            ->at('11:30')
            ->environments(['production'])
            ->onFailure(function () {
                \Log::channel('slack')->critical(config('app.name') . ' send daily issue email failed');
            });

        $schedule
            ->command('update-statistics')
            ->dailyAt('22:30')
            ->environments(['production'])
            ->onFailure(function () {
                \Log::channel('slack')->critical(config('app.name') . ' update statistics failed');
            });

        $schedule
            ->command('backup:run --only-db')
            ->dailyAt('2:15')
            // ->environments(['production'])
            ->onFailure(function () {
                Log::channel('slack')->critical(config('app.name') . ' database backup failed.');
            });

        $schedule
            ->command('backup-logs')
            ->dailyAt('2:30')
            ->onFailure(function () {
                \Log::channel('slack')->critical(config('app.name') . ' backup logs failed.');
            });

        $schedule
            ->command('backup-order-logs')
            ->dailyAt('2:00')
            ->onFailure(function () {
                \Log::channel('slack')->critical(config('app.name') . ' backup logs failed.');
            });

        $schedule
            ->command('backup-env')
            ->monthly()
            // ->dailyAt('2:00')
            ->onFailure(function () {
                \Log::channel('slack')->critical(config('app.name') . ' backup env failed.');
            });

        $schedule
            ->command('heartbeat')
            ->hourly();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
