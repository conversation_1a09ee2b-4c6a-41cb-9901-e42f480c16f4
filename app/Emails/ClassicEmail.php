<?php

namespace App\Emails;

use CS_REST_Transactional_ClassicEmail;

abstract class ClassicEmail
{
    protected $ccs;
    protected $bccs;
    protected $text;
    protected $apiKey;
    protected $replyTo;
    protected $data = [];

    public function __construct()
    {
        $this->replyTo = config('mail.from.name') . '<' . config('mail.from.address') . '>';
        $this->apiKey = config('services.campaign_monitor');
    }
    abstract protected function html();

    abstract protected function from();
    abstract protected function subject();
    abstract protected function groupName();

    public function sendTo($user)
    {
        $mailer = $this->newTransaction();

        $data = array_merge(compact('user'), $this->data);

        return $mailer->send(
            [
                'From' => $this->from(),
                'ReplyTo' => $this->replyTo,
                'To' => [data_get($user, 'name') . ' <' . data_get($user, 'email') . '>'],
                'CC' => $this->ccs,
                'BCC' => $this->bccs,
                'Subject' => $this->subject(),
                'Html' => $this->html(),
                'Text' => $this->text,
                // 'Attachments' => [
                //     "Name" => 'string required',
                //     "Type" => 'string required',
                //     "Content" => 'string required',
                // ]
            ],
            $this->groupName() ?? NULL,
            'Yes'
        );
    }

    // public function withData($data)
    // {
    //     $this->data = $data;

    //     return $this;
    // }

    public function addCCs($users)
    {
        $this->ccs = collect($users)
            ->map(function ($user) {
                return data_get($user, 'name') . ' <' . data_get($user, 'email') . '>';
            });
        return $this;
    }

    public function addBCCs($users)
    {
        $this->bccs = collect($users)
            ->map(function ($user) {
                return data_get($user, 'name') . ' <' . data_get($user, 'email') . '>';
            });

        return $this;
    }

    public function replyTo($email)
    {
        $this->replyTo = $email;

        return $this;
    }

    public function addtext($text)
    {
        $this->text = $text;

        return $this;
    }

    // public function groupName()
    // {
    //     return Str::of(class_basename(get_class($this)))
    //         ->snake()
    //         ->replace('_', ' ')
    //         ->title()
    //         ->remove(' Email');
    // }

    public function newTransaction()
    {
        return new CS_REST_Transactional_ClassicEmail(
            ['api_key' => $this->apiKey]
        );
    }
}
