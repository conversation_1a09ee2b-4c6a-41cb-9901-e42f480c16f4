<?php

namespace App\Emails;

class DailyErrorEmail extends ClassicEmail
{
    protected $issues;

    public function __construct($issues)
    {
        parent::__construct();
        $this->issues = $issues;
    }

    public function html()
    {
        return view(
            'emails.dailyError',
            ['issues' => $this->issues,]
        )
            ->render();
    }

    public function from()
    {
        return "P4L Shipping <<EMAIL>>";
    }

    public function subject()
    {
        return now()->toDateString() . ' Unresolved Shipping Issues';
    }

    public function groupName()
    {
        return 'Daily Error';
    }

    // public function getEmailId()
    // {
    //     return '3ce3d2a8-01da-467c-99a3-886465dd297b';
    // }

    // public function variables()
    // {
    //     return [
    //         'link',
    //         'issue',
    //         'order_id',
    //         'shipper',
    //     ];
    // }
}
