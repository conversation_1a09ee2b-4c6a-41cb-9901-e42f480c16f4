<?php

namespace App\Emails;

use CS_REST_Transactional_SmartEmail;

abstract class SmartEmail
{
    protected $ccs;
    protected $bccs;
    protected $apiKey;
    protected $data = [];

    public function __construct()
    {
        $this->apiKey = config('services.campaign_monitor');
    }

    abstract protected function getEmailId();

    public function sendTo($user)
    {
        $mailer = $this->newTransaction();

        $data = array_merge(compact('user'), $this->data);

        return $mailer->send([
            'To' => data_get($user, 'name') . '<' . data_get($user, 'email') . '>',
            'CC' => $this->ccs,
            'BCC' => $this->bccs,
            'Data' => $data
        ], 'Yes');
    }

    public function withData($data)
    {
        $this->data = $data;

        return $this;
    }

    public function addCCs($users)
    {
        $this->ccs = collect($users)->pluck('email');

        return $this;
    }

    public function addBCCs($users)
    {
        $this->bccs = collect($users)->pluck('email');

        return $this;
    }

    public function newTransaction()
    {
        return new CS_REST_Transactional_SmartEmail(
            $this->getEmailId(),
            ['api_key' => $this->apiKey]
        );
    }
}
