<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    // public function render($request, Throwable $e)
    // {
    //     if ($this->isHttpException($e)) {
    //         // if ($th instanceof \Symfony\Component\HttpKernel\Exception\HttpException && $th->getStatusCode() == 400) {
    //         if ($e->getCode() == 500) {
    //             dd($e->getMessage());
    //             return response()->view('errors.500', ['message' => $e->getMessage()], 500);
    //         }
    //     }
    //     return parent::render($request, $e);
    // }
}
