<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    // protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    protected function credentials(Request $request)
    {
        $credentials = $request->only($this->username(), 'password');
        $credentials['status'] = 'active';

        return $credentials;
    }

    protected function authenticated(Request $request, $user)
    {
        // if the user tries to access ship2 and he is not allowed to we redirect him
        if (config('app.env') != 'production' && $user->role_id == 2) {
            auth()->logout();
            return redirect()->away('https://shipping.p4less.com');
        }
        //only devs can access ship-dev
        if (config('app.env') == 'dev' && $user->role_id != 3) {
            auth()->logout();
            return redirect()->away('https://shipping.p4less.com');
        }

        if ($request->cookie('label-printer') == null || $request->cookie('label-printer') == '') {
            return redirect('/user');
        } else {
            return redirect()->intended();
        }
    }
}
