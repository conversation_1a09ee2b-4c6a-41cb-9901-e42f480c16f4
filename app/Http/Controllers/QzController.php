<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class QzController extends Controller
{
    public static function sign($request) {
        $keyFile = Storage::disk('local')->get('qz/private-key.pem');
        
        $privateKey = openssl_get_privatekey($keyFile);
        
        $signature = null;
        openssl_sign($request, $signature, $privateKey, "sha512"); // Use "sha1" for QZ Tray 2.0 and older
        
        if ($signature) {
           return base64_encode($signature);
        }
        
        throw new \Exception("Error Processing QZ Signature Request", 1);
    }
}
