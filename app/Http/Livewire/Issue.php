<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Library\Services\PrintNodeAPI;
use App\Library\Services\ShipEngineAPI;
use App\Library\Services\SellerCloudAPI;
use App\Models\FailedApiRequest;
use App\Models\Shipment;
use App\Models\User;
use Log;

class Issue extends Component
{
    public $issue;

    public function render()
    {
        return view('livewire.issue');
    }

    public function shipLocal($url, $title, $failedApiRequestId)
    {
        $printNodeAPI = new PrintNodeAPI;
        $printJob = $printNodeAPI->print($url, $title, data_get(FailedApiRequest::find($failedApiRequestId), 'order_id'));

        if (!$printJob || $printJob['state'] != 'done') {
            session()->flash('error', 'Unable to print');
            throw new \Exception("Unable to print", 1);
        }

        $this->resendSellerCloud($failedApiRequestId);
        session()->flash('message', "Sent to printer");
        return redirect(request()->header('Referer'));
    }

    public function shipShipper($url, $title, $failedApiRequestId)
    {
        $printNodeAPI = new PrintNodeAPI;
        $failedApiRequest = FailedApiRequest::find($failedApiRequestId);
        $printJob = $printNodeAPI->print($url, $title, data_get($failedApiRequest, 'order_id'), data_get($failedApiRequest, 'printer_id'));

        if (!$printJob || $printJob['state'] != 'done') {
            session()->flash('error', 'Unable to print');
            throw new \Exception("Unable to print", 1);
        }

        $this->resendSellerCloud($failedApiRequestId);
        session()->flash('message', "Sent to printer");
        return redirect(request()->header('Referer'));
    }

    public function resendSellerCloud($failedApiRequestId)
    {
        session()->flash('message', "Sending the order to Sellercloud");

        $failedApiRequest =  FailedApiRequest::find($failedApiRequestId);
        $order_id = $failedApiRequest->order_id;

        $sellerCloudAPI = new SellerCloudAPI;
        $sellerCloudAPI->shipPackage($failedApiRequest->order, $failedApiRequest->label);

        $order = $sellerCloudAPI->getOrder($order_id);

        if ($order['Statuses']['ShippingStatus'] == 3) {

            $sellerCloudAPI->addCustomerServiceNote($failedApiRequest->order['OrderID'], 'Shipped by: ' . optional(Shipment::where('order_id', $failedApiRequest->order_id)->where('voided', 0)->first())->user->email);

            (new Ship)
                ->emptySortbox(
                    $failedApiRequest->sortbox_id,
                    $failedApiRequest->order,
                    $failedApiRequest->label
                );

            $failedApiRequest->update([
                'status' => 'Resolved',
                'resolved_by' => optional(optional(auth())->user())->name??'CLI',
            ]);
            // if ($order['Statuses']['ShippingStatus'] != 3) {
        } else {
            $failedApiRequest->update(
                [
                    'type' => 'SellerCloud',
                    'error' =>  'Not marked as shipped on Sellercloud',
                ]
            );
            session()->flash('error', 'An error occurred please try again or update Sellercloud manualy');
            logOrder($order['OrderID'], 'Not marked as shipped on Sellercloud by manual retry');
        }
        return redirect(request()->header('Referer'));
    }

    public function resendSortBox($failedApiRequestId)
    {
        session()->flash('message', "Emptying SkuBlox");

        $failedApiRequest =  FailedApiRequest::find($failedApiRequestId);

        (new Ship)
            ->emptySortbox(
                $failedApiRequest->sortbox_id,
                $failedApiRequest->order,
                $failedApiRequest->label
            );
    }

    public function manualResolved($failedApiRequestId)
    {
        FailedApiRequest::find($failedApiRequestId)
            ->update([
                'status' => 'manual_resolved',
                'resolved_by' => auth()->user()->name,
            ]);
        session()->flash('message', "Thanks for keeping our system updated");
        return redirect(request()->header('Referer'));
    }
}
