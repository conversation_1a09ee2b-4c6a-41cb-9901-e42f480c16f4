<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Library\Services\QzAPI;
use App\Models\FailedApiRequest;
use App\Models\User;
use App\Models\Shipment;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Log;
use Livewire\WithPagination;

class Issues extends Component
{
    use WithPagination;
    use AuthorizesRequests;

    public $labels = [];
    public $users = [];
    public $statuses = [];
    public $columns = [
        'Issue / Order',
        // 'Label',
        'Shipping Date / Shipper', 'Status', 'Resolver / Resolved Date', ''
    ];
    public $loading_message = "";
    public $perPage = 25;

    public $user;
    public $search;
    public $status;

    protected $queryString = ['status', 'search' => ['except' => ''], 'user' => ['except' => ''],];

    public function render()
    {
        $issues = $this->getIssues();

        return view('livewire.issues', [
            'issues' => $issues
        ]);
    }

    public function mount()
    {
        $user = auth()->user();

        abort_unless(isAdmin(), 403);

        $this->users = User::all();
        // $this->user = $user->id;

        $this->statuses = $this->getStatuses();
    }

    public function resetFilter()
    {
        $this->user = null;
        $this->search = null;
        $this->status = null;
    }

    private function getIssues()
    {
        $search = $this->search;

        $issues = FailedApiRequest::latest();
        if (!empty($this->status)) {
            $issues->where('status', $this->status);
        }
        // if (!empty($this->user)) {
        //     $issues->where(function ($query) {
        //         return  $query->whereHas('shipment.user', function ($query) {
        //             return $query->where('id', $this->user);
        //         });
        //     });
        // }
        if (!empty($search)) {
            $issues->where('order_id', 'like', "%" . $search . "%");
        }

        return $issues->paginate($this->perPage);
    }

    private function getStatuses()
    {
        // return FailedApiRequest::groupBy('status')->pluck('status')->toArray();
        return FailedApiRequest::pluck('status')->unique()->toArray();
    }
}
