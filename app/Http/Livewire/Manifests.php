<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Library\Services\ShipEngineAPI;
use App\Models\Setting;
use Log;
use Livewire\WithPagination;

class Manifests extends Component
{
    use WithPagination;

    public array $carriers = [];
    public array $manifests = [];
    public array $previousManifests = [];

    public $columns = ['ID', 'Carrier', 'Date', 'Shipments','Warehouse', ''];

    public array $days = [];
    public string $date = '';
    public $warehouseId;
    public $warehouses;

    public function mount() 
    {
        $this->warehouses = Setting::all();
        $this->date = date("D, F j, Y", strtotime("today"));
        // $this->days = [
        //     date("D, F j, Y", strtotime("today")),
        //     date("D, F j, Y", strtotime("+1 days")),
        //     date("D, F j, Y", strtotime("+2 days")),
        //     date("D, F j, Y", strtotime("yesterday")),
        //     date("D, F j, Y", strtotime("-2 days")),
        //     date("D, F j, Y", strtotime("-3 days")),
        //     date("D, F j, Y", strtotime("-4 days")),
        // ];
        $shipEngineAPI = new ShipEngineAPI;
        $this->carriers = $shipEngineAPI->getCarriers();
    }

    public function render()
    {
        return view('livewire.manifests');
    }

    public function loadList() {
        try {
            $shipEngineAPI = new ShipEngineAPI;
            $response = $shipEngineAPI->getManifests();

            if(array_key_exists('manifests', $response)) {
                $previousManifests = $response['manifests'];

                foreach ($previousManifests as &$manifest) {
                    $manifest['carrier'] = \Arr::first($this->carriers, function($value, $key) use ($manifest) {
                        return $manifest['carrier_id'] == $value['carrier_id'];
                    }); 
                   
                    $manifest['warehouse'] = collect($this->warehouses)->filter(function ($warehouse) use ($manifest) {
                            return $warehouse->seWarehouse == $manifest['warehouse_id'];
                        })->first()->name;
                }
        
                $this->previousManifests = $previousManifests;

            } elseif((array_key_exists('errors', $response) && count($response['errors']))) {
                throw new \Exception($response['errors'][0]['message'], 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::debug($e->getMessage());
        }
    }

    public function createManifest($carrierId) {
        $date = now()->parse($this->date)->toIso8601String();
        try {
            $shipEngineAPI = new ShipEngineAPI;
            $response = $shipEngineAPI->createManifest($carrierId, $date, $this->warehouseId);

            if(array_key_exists('manifests', $response)) {
                $this->manifests = $response['manifests'];
                $this->loadList();
            } elseif((array_key_exists('errors', $response) && count($response['errors']))) {
                throw new \Exception($response['errors'][0]['message'], 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::debug($e->getMessage());
        }
    }
}
