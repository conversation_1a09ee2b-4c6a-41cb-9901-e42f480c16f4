<?php

namespace App\Http\Livewire;

use App\Library\Services\SellerCloudLightwallAPI;
use Livewire\Component;

class Order extends Component
{
    public $order;

    public function render()
    {
        return view('livewire.order');
    }

    public function toggleDone($index)
    {
        $this->emit('toggleNoteDone', $index);
    }

    public function flashLight($sortBoxid)
    {
        $sellercloudLightWall = new SellerCloudLightwallAPI;
        $sellercloudLightWall->flashLight($sortBoxid);
    }
}
