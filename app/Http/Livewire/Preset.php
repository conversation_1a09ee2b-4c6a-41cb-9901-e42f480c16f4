<?php

namespace App\Http\Livewire;

use App\Models\ShippingBox;
use Livewire\Component;
use Illuminate\Support\Str;

class Preset extends Component
{


    protected $rules = [
        'preset.weight' => '',
        'preset.locked' => '',
        'preset.box_id' => '',
    ];

    public $preset;
    public $presetId;
    public $skus;
    public $boxes = [];
    public $box;



    public function render()
    {
        return view('livewire.preset');
    }

    public function mount()
    {
        $this->skus = Str::of($this->preset->skus)->split('(__)')->map(function ($sku) {
            return explode('_', $sku);
            // $pos = strrpos($sku, '_');
            // return  [substr($sku, 0, $pos), substr($sku, -1, $pos)];
        });

        $box = ShippingBox::find($this->preset->box_id);
        if ($box) {
           $this->box = $box['preset_name'] . ' ' .$box['width'] . 'x' . $box['height'] . 'x' . $box['depth'];
        }else{
            $this->box = "";
        }

         //->prepend('select', 0);
        // $this->preset = DB::table('shipping_statistics')->where('id', '=', $this->preset);

        // if (!$this->preset) {
        //     abort(404);
        // }
    }
}
