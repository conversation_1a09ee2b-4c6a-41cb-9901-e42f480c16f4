<?php

namespace App\Http\Livewire;

use App\Models\ShippingStatistics;
use Livewire\Component;
use Livewire\WithPagination;


class Presets extends Component
{
    use WithPagination;

    public $perPage = 25;
    public $filter = [
        "search" => "",
    ];
    public $columns = ['Sku /Quantity', 'Box', 'Weight', 'Locked', ''];

    public function render()
    {
        $this->presets = $this->getPresets();

        return view('livewire.presets', ['presets' => $this->presets]);
    }

    // public function resetFilter()
    // {
    //     foreach ($this->filter as $key => $value) {
    //         $this->filter[$key] = '';
    //     }

    //     $this->presets = $this->getPresets();
    // }

    private function getPresets()
    {
        $search = $this->filter['search'];

        $presets = ShippingStatistics::latest();

        if (!empty($search)) {
            $presets->where('skus', 'like', "%" . $search . "%");
        }

        return $presets->paginate($this->perPage);
    }
}
