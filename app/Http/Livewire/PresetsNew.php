<?php

namespace App\Http\Livewire;

use Illuminate\Support\Str;
use Livewire\Component;
use App\Models\ShippingBox;
use App\Models\ShippingStatistics;

class PresetsNew extends Component
{
    public $shippingStatistic;
    public $shippingStatisticId;
    public $boxes = [];
    public $box_id;
    public $weight;
    public $locked;
    public $redirect;
    public $skus = [];


    public function render()
    {
        return view('livewire.presets-new');
    }

    public function mount()
    {
        if ($this->shippingStatisticId == 'new') {
            $this->shippingStatistic = new ShippingStatistics;
        }else {
            $this->shippingStatistic = ShippingStatistics::find($this->shippingStatisticId);
            $this->redirect =  request()->header('Referer');
            if (!$this->shippingStatistic) {
               abort(404);
            }
        }
        $this->skus = !!$this->shippingStatistic->skus ? 
        collect(explode('__', $this->shippingStatistic->skus))->map(function ($sku){
            $sku = explode('_',$sku);
            return ['sku' => data_get($sku ,'0'),'quantity'=> data_get($sku, '1')];
            })->toArray() :
        [['sku' => '','quantity' => '']];
        $this->boxes = ShippingBox::all();
        $this->box_id =$this->shippingStatistic->box_id ?? null ;
        $this->weight =$this->shippingStatistic->weight ?? null ;
        $this->locked =$this->shippingStatistic->locked ?? null ;
    }

    public function removePreset($index)
    {
        if (collect($this->skus)->count() == 1) {
            return ;
        }
      return  array_splice($this->skus,$index,1);
    }

    public function addPreset($index)
    {
        return array_splice($this->skus,$index+1,0,[['sku'=>'','quantity'=>""]]);
    }

    public function savePreset()
    {
        if(collect($this->skus)->pluck('sku')->contains('')){
            session()->flash('error', 'All Sku field are required');
            return;
        }
        if(collect($this->skus)->pluck('quantity')->contains('')){
            session()->flash('error', 'All Quantity field are required');
            return;
        }
        try {
            $this->validate([
                'weight' => 'required',
                'box_id' =>'required|integer',
            ]);
      
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors = collect($e->errors())->flatten()->join(' ');
            session()->flash('error', $errors);

            throw $e;
        }
        
      $skus = collect($this->skus)->map(function ($sku){
            return collect($sku)->join('_');
        })->join('__');

        if ($this->shippingStatisticId == 'new' && $existing = ShippingStatistics::where('skus',$skus)->first()) {
            redirect("/presets/$existing->id");
            session()->flash('error', ' A preset exists with these skus you can edit the preset here.');
           return;
        }

        $this->shippingStatistic->skus = $skus;
        $this->shippingStatistic->box_id = $this->box_id;
        $this->shippingStatistic->weight = $this->weight;
        $this->shippingStatistic->locked = $this->locked;
        $this->shippingStatistic->save();
        return ($this->redirect && Str::of($this->redirect)->contains('?')) ? redirect($this->redirect) : redirect('/presets');
    }

}
