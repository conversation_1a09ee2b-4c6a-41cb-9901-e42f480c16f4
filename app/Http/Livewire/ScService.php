<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\ServiceMapping;
use App\Models\ScService as ScServiceModel;

class ScService extends Component
{
    public $service;
    public $serviceName;
    public $seServices = [];
    public $serviceMappings = [];
    public $mapToGroup = TRUE;

    protected $rules = [
        'service.service_id' => '',
        'service.service_mapping_id' => '',
    ];

    public function mount() {
        if(!$this->service && isset($this->serviceName)) {
            $this->service = ScServiceModel::updateOrCreate(
                ['sellercloud_name' => $this->serviceName],
                []
            );
        }

        $this->mapToGroup = $this->service->service_mapping_id && !$this->service->service_id;
    }

    public function render()
    {
        return view('livewire.sc-service');
    }

    public function updatingmapToGroup($value) {
        if($value) {
            $this->service->service_id = NULL;
        } else {
            $this->service->service_mapping_id = NULL;
        }
    }

    public function updated() {
        $scName = $this->service->sellercloud_name;
        $serviceId = $this->service->service_id;
        $serviceMappingId = $this->service->service_mapping_id;

        sendAdminMail("New SC mapping by user: " . auth()->user()->email . ", sc code: $scName, mapped to service ID: $serviceId, service group: $serviceMappingId");
        $this->service->save();
    }
}
