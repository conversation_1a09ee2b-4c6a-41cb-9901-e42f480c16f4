<?php

namespace App\Http\Livewire;

use Livewire\Component;

class SeService extends Component
{
    public $service;
    public $serviceMappings;

    protected $rules = [
        'service.id' => '',
        'service.service_mapping_id' => '',
        'service.active' => '',
        'service.use_for_prime_rush' => '',
    ];
    
    public function render()
    {
        return view('livewire.se-service');
    }

    public function updated() {
        $this->service->save();
    }
}
