<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Library\Services\ShipEngineAPI;
use App\Models\Carrier;
use App\Models\PackageType;
use App\Models\Service;
use App\Models\Shipment;
use App\Models\Setting;
use App\Models\ScService;
use App\Models\SeService;
use App\Models\ServiceMapping;

class Settings extends Component
{
    public $carriers = [];
    public $settings;

    public $seServices;
    public $scServices;
    public $serviceMappings;

    public $scColumns = ['ID', 'Sellercloud Name', 'Map to', 'Mapping'];
    public $seColumns = ['ID', 'Carrier Code', 'Speed', 'Service Code', 'Name', 'Active', 'Prime Rush'];

    protected $rules = [
        'settings.seAPIKey' => '',
        'settings.seWarehouse' => '',
        'settings.weightLeeway' => 'required|max:2|integer',
    ];

    public function mount()
    {
        $this->settings = Setting::firstOrNew();
        $this->scServices = ScService::all();
        $this->seServices = SeService::all();
        $this->serviceMappings = ServiceMapping::all();
    }

    public function render()
    {
        return view('livewire.settings');
    }

    public function updatedsettings($value)
    {
        $this->settings->save();
        $this->importCarriers();
    }

    public function importCarriers()
    {
        $shipEngineAPI = new ShipEngineAPI;
        $this->carriers = $shipEngineAPI->getCarriers();

        $carrierUpdates = [];
        $serviceUpdates = [];
        $packageUpdates = [];

        foreach ($this->carriers as $carrier) {
            $carrierUpdates[] = [
                'carrier_id' => $carrier['carrier_id'],
                'carrier_code' => $carrier['carrier_code'],
                'account_number' => $carrier['account_number'],
                'requires_funded_amount' => $carrier['requires_funded_amount'],
                'balance' => $carrier['balance'],
                'nickname' => $carrier['nickname'],
                'friendly_name' => $carrier['friendly_name'],
                'primary' => $carrier['primary'],
                'has_multi_package_supporting_services' => $carrier['has_multi_package_supporting_services'],
                'supports_label_messages' => $carrier['supports_label_messages'],
                'active' => 1
            ];

            foreach ($carrier['services'] as $service) {
                $serviceUpdates[] = [
                    'carrier_code' => $service['carrier_code'],
                    'service_code' => $service['service_code'],
                    'name' => $service['name'],
                    'domestic' => $service['domestic'],
                    'international' => $service['international'],
                    'is_multi_package_supported' => $service['is_multi_package_supported'],
                ];
            }

            foreach ($carrier['packages'] as $package) {
                $packageUpdates[] = [
                    'carrier_code' => $carrier['carrier_code'],
                    'package_id' => $package['package_id'],
                    'package_code' => $package['package_code'],
                    'name' => $package['name'],
                    'description' => $package['description']
                ];
            }
        }

        Carrier::upsert(
            $carrierUpdates,
            ['carrier_id'],
            [
                'carrier_code',
                'account_number',
                'requires_funded_amount',
                'balance',
                'nickname',
                'friendly_name',
                'primary',
                'has_multi_package_supporting_services',
                'supports_label_messages',
                'active'
            ]
        );

        Service::upsert(
            $serviceUpdates,
            ['carrier_code', 'service_code'],
            [
                'name',
                'domestic',
                'international',
                'is_multi_package_supported'
            ]
        );

        PackageType::upsert(
            $packageUpdates,
            ['carrier_code', 'package_code'],
            [
                'name',
                'description'
            ]
        );
        $collection = Carrier::whereNotIn('carrier_id', array_column($this->carriers, 'carrier_id'))->where('active', true)->get(['carrier_code', 'carrier_id']);
        if ($collection->count()) {
            $string = $collection->map(function ($carrier) {
                // dd($carrier);
                return data_get($carrier, 'carrier_code') . ' ' .  data_get($carrier, 'carrier_id');
            })->join(' ,');

            sendAdminMail(config('app.name') . ' ' . $string . ' is set to inactive');
        }
        Carrier::whereNotIn('carrier_id', array_column($this->carriers, 'carrier_id'))->update(['active' => 0]);
    }


    public function voidedShipments()
    {
        $shipEngineAPI = new ShipEngineAPI;
        $voidedShipments = $shipEngineAPI->getLabels(TRUE);

        $count = Shipment::whereIn('label_id', array_column($voidedShipments, 'label_id'))
            ->where('status', '!=', 'voided')
            ->count();

        logOrder(
            'voided',
            $count ? "$count voided" : $count
        );

        Shipment::whereIn('label_id', array_column($voidedShipments, 'label_id'))
            ->update(['voided' => 1, 'status' => 'voided']);
    }
}
