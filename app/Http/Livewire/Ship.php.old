<?php

namespace App\Http\Livewire;

use App\Emails\PrintNodeErrorEmail;
use App\Jobs\SendOrderToSellerCloud;
use App\Library\Services\FabbleAPI;
use Livewire\Component;
use App\Library\Services\SellerCloudAPI;
use App\Library\Services\SellerCloudLightwallAPI;
use App\Library\Services\SellerCloudSoapAPI;
use App\Library\Services\ShipEngineAPI;
use App\Library\Services\PrintNodeAPI;
use App\Library\Services\QzAPI;
use Illuminate\Support\Facades\Http;
use App\Models\Shipment;
use App\Models\Carrier;
use App\Models\FailedApiRequest;
use App\Models\Setting;
use App\Models\ScService;
use App\Models\SeService;
use App\Models\ServiceMapping;
use App\Models\ShippingBox;
use App\Models\ShippingStatistics;
use App\Models\User;
use DB;
use Log;
use Auth;
use Exception;
use Illuminate\Support\Str;
use Settings;

class Ship extends Component
{
    public $user;
    public $settings;

    public $order;
    public $picklist;

    public $carriers;
    public $rates = [];
    public $allRates;
    public $selectedRate;
    public string $rateReason = '';

    public array $packageTypes = [];
    public array $selectedPackageTypes = [];

    public array $shippingBoxes = [];
    public int $selectedShippingBox = -1;

    public array $serviceCodes = [];

    public $label;
    public string $printNodeApiKey = '';

    public $sortboxId;

    public $serviceMappings = [];
    public $seServices = [];
    public $needsService;
    public $service;
    public $count = 0;

    public $shippingStatistic;
    public $skus;
    public $pin;
    public $failed;
    public $confirmReset = false;
    public $gettingRates = false;
    public $simplifiedView = true;

    public $rules = [
        'service.service_mapping_id' => ''
    ];

    // public string $status = '';
    // public string $statusType = '';

    protected $listeners = ['printQZLabel' => 'printQZLabel', 'toggleNoteDone'];

    public function mount()
    {
        request()->cookie('label-printer') ?? session()->flash('error', "No label printer selected, please select a label printer");
        // session()->flush();
        $this->user = auth()->user();
        $this->settings = Setting::first();
        $printNodeAPI = new PrintNodeAPI;
        $this->printNodeApiKey = $printNodeAPI->getApiKey();
        $this->getCarriers();
        $this->getServices();
        $this->getServiceMappings();
        $this->setShippingBoxes();

        $this->setFromSession();
        $this->setView();
        $this->countOrders();
        // $this->getPackageTypes();
    }

    public function render()
    {
        return view('livewire.ship');
    }

    public function updated($name, $value)
    {
        session()->put('ship_order', $this->order);
        session()->put('ship_rates', $this->rates);
        session()->put('ship_needsService', $this->needsService);
        session()->put('ship_service', $this->service);
    }

    public function updatedselectedShippingBox($value)
    {
        if ($value < 1) {
            $this->selectedShippingBox = $value;
            $this->order['OrderPackages'][0]['Length'] = null;
            $this->order['OrderPackages'][0]['Width'] = null;
            $this->order['OrderPackages'][0]['Height'] = null;
            session()->put('ship_order', $this->order);
            session()->put('ship_selectedShippingBox', $this->selectedShippingBox);
            return;
        }

        $shippingBox = $this->shippingBoxes[array_search($value, array_column($this->shippingBoxes, 'id'))];
        $this->order['OrderPackages'][0]['Length'] = $shippingBox['depth'];
        $this->order['OrderPackages'][0]['Width'] = $shippingBox['width'];
        $this->order['OrderPackages'][0]['Height'] = $shippingBox['height'];
        session()->put('ship_order', $this->order);
        session()->put('ship_selectedShippingBox', $this->selectedShippingBox);
    }

    public function updatingselectedRate($value)
    {
        $this->rates[$value]['selected'] = TRUE;
        session()->put('ship_selectedRate', $value);
    }

    private function setFromSession()
    {
        $sessions = session()->all();
        foreach ($sessions as $name => $value) {
            if (strpos($name, 'ship_') === 0) {
                $key = str_replace('ship_', '', $name);
                $this->$key = $value;
            }
        }
    }

    private function getCarriers()
    {
        $shipEngineAPI = new ShipEngineAPI;

        $this->status = "Getting all carriers...";
        $this->statusType = "info";
        $this->carriers = Carrier::where('active', TRUE)->get(); //$shipEngineAPI->getCarriers();

        // foreach ($this->carriers as $carrier) {
        //     $services = $carrier['services'];
        //     foreach ($services as $service) {
        //         if(!in_array($service['service_code'], $excludedServices)) {
        //             $this->serviceCodes[] = $service['service_code'];
        //         }
        //     }
        // }
    }

    private function getServices()
    {
        $this->seServices = SeService::all();
    }

    // private function getPackageTypes() {
    //     $shipEngineAPI = new ShipEngineAPI;
    //     $this->status = "Getting all package types...";
    //     $this->statusType = "info";
    //     $this->packageTypes = $shipEngineAPI->getPackageTypes($this->selectedCarriers);
    //     $this->status = "Ready to ship. Enter an order ID";
    //     $this->statusType = "success";
    // }

    private function getServiceMappings()
    {
        $this->serviceMappings = ServiceMapping::all();
    }

    private function setShippingBoxes()
    {
        $user = auth()->user();
        // $this->shippingBoxes = $user->shipping_boxes->toArray();
        $this->shippingBoxes = ShippingBox::all()->toArray();
    }

    private function tryOneRate()
    {
        // First check if package is < 50LB
        if (
            $this->order['ShippingDetails']['EstShipWeight']['Pounds'] * 16 + $this->order['ShippingDetails']['EstShipWeight']['Ounces'] <= 800 &&
            $this->order['OrderPackages'][0]['Length'] * $this->order['OrderPackages'][0]['Width'] * $this->order['OrderPackages'][0]['Height'] <= 644
            //&& array_column($this->packageTypes, 'package_code', 'fedex_pak_onerate')
            && $this->order['isPrime']
        ) {
            $this->order['isOneRate'] = TRUE;
            // TODO we need to still figure out a way to also get regular Fedex rates when we request one_rate. Regular might be cheaper.
            // $this->selectedPackageTypes = array_column($this->carriers[array_search('fedex', array_column($this->carriers, 'carrier_code'))]['packages'], 'carrier_code');

            // We don't do OneRate anymore, except for Amazon Prime.
            // $this->selectedPackageTypes = ['fedex_pak_onerate'];
        } else {
            $this->order['isOneRate'] = FALSE;
        }
    }

    private function getCheapestOneRate()
    {
        $response = $this->makeRateRequest(TRUE);

        if (count($response)) {
            $rates = $this->sortRates($response['rate_response']['rates']);
            return $rates[0];
        } else {
            return [];
        }
    }

    private function sortRates($rates)
    {
        return collect($rates)
            ->map(function ($rate) {
                $price = 0;
                if (
                    ($this->order['OrderPackages'][0]['Length'] >= 22 ||
                        $this->order['OrderPackages'][0]['Width'] >= 22 ||
                        $this->order['OrderPackages'][0]['Height'] >= 22
                    )
                    &&  (str_contains(data_get($rate,  'service_code'), 'usps') ||
                        data_get($rate, 'carrier_code') == 'endicia'
                    )
                ) {
                    $price = 4;
                }

                if (
                    ($this->order['OrderPackages'][0]['Length'] >= 30 ||
                        $this->order['OrderPackages'][0]['Width'] >= 30 ||
                        $this->order['OrderPackages'][0]['Height'] >= 30 ||
                        // 2 cubic feet
                        ($this->order['OrderPackages'][0]['Length'] *
                            $this->order['OrderPackages'][0]['Width'] *
                            $this->order['OrderPackages'][0]['Height'] / (12 * 12 * 12) >= 2
                        ))
                    &&  (str_contains(data_get($rate,  'service_code'), 'usps') ||
                        data_get($rate, 'carrier_code') == 'endicia'
                    )
                ) {
                    $price = 15;
                }

                if (data_get($rate, 'carrier_code') ==  'amazon_buy_shipping') {
                    $price = $price + 0.14;
                }

                $rate['calculated_price'] = $price + data_get($rate, 'shipping_amount.amount')
                    + data_get($rate, 'insurance_amount.amount')
                    + data_get($rate, 'confirmation_amount.amount')
                    + data_get($rate, 'other_amount.amount');

                return $rate;
            })
            ->sortBy('calculated_price')
            ->toArray();
    }

    private function addSignature()
    {
        $this->order['requiresSignature'] = false;

        // OrderSource 1 is Ebay
        if ($this->order['TotalInfo']['SubTotal'] >= 400 && $this->order['OrderDetails']['OrderSource'] == 1) {
            $this->order['requiresSignature'] = TRUE;
            return;
        }
        // todo
        // remove for beta photo4lesss goes down
        // OrderSource 6 is website
        // if we have invoice then its fabble not photo4less
        // if ($this->order['TotalInfo']['SubTotal'] >= 500 && $this->order['OrderDetails']['OrderSource'] == 6 && data_get($this->order, 'invoice')) {
        //     $this->order['requiresSignature'] = TRUE;
        //     return;
        // }

        if ($this->order['OrderDetails']['OrderSource'] == 6 && data_get($this->order, 'invoice')) {
            if ($this->order['TotalInfo']['SubTotal'] >= 500 ) {
                  $this->order['requiresSignature'] = TRUE;
            }
            return;
        }

        if (($this->order['TotalInfo']['SubTotal'] >= 250 && $this->order['OrderDetails']['OrderSource'] != 1) || $this->order['TotalInfo']['SubTotal'] >= 400) {
            $this->order['requiresSignature'] = TRUE;
        }
    }

    private function makeRateRequest($oneRate = FALSE)
    {
        try {
            $shipEngineAPI = new ShipEngineAPI;
            $this->order['rateRequest'] = $this->buildShipRequest($oneRate);
            $response = $shipEngineAPI->getRates($this->order['rateRequest'], $this->order['OrderID']);

            if ((array_key_exists('errors', $response) && count($response['errors']))) {
                // $this->status = $response['errors'][0]['message'];
                // $this->statusType = "error";
                // session()->flash('message', $response['errors'][0]['message']);
                throw new \Exception($response['errors'][0]['message'], 1);
            } else {
                if (array_key_exists('errors', $response['rate_response']) && count($response['rate_response']['errors'])) {
                    // $this->status = $response['rate_response']['errors'][0]['message'];
                    // $this->statusType = "error";
                    session()->flash('message', $response['rate_response']['errors'][0]['message']);
                }

                return $response;
            }
        } catch (\Exception $e) {
            // $this->status = $e->getMessage();
            // $this->statusType = "error";
            report($e);
            session()->flash('message', $e->getMessage());
            logOrder($this->order['OrderID'], $e->getMessage());
            // Log::error($e->getMessage());
        }

        return [];
    }

    public function getRates($ship = false)
    {
        $notDoneGifts =  optional(collect(data_get($this->order, 'items_gift')))->filter(function ($gift) {
            return !data_get($gift, 'confirmed');
        })->keys()->map(function ($key) {
            return $key + 1;
        });;
        if (!!$notDoneGifts->count()) {
            session()->flash('error', "Please finish " . Str::of('Gift')->plural($notDoneGifts->count()) . ' ' . $notDoneGifts->join(', '));
            return;
        }
        $notDoneGiftcards =  optional(collect(data_get($this->order, 'gift_cards')))->filter(function ($giftCard) {
            return !array_key_exists('printed', $giftCard);
            // return !data_get($giftCard, 'printed');
        })->keys()->map(function ($key) {
            return $key + 1;
        });;
        if (!!$notDoneGiftcards->count()) {
            session()->flash('error', "Please complete printing " . Str::of('Gift Card')->plural($notDoneGiftcards->count()) . ' ' . $notDoneGiftcards->join(', '));
            return;
        }

        $this->gettingRates = true;

        if (
            !$this->order['OrderPackages'][0]['Length'] ||
            !$this->order['OrderPackages'][0]['Width'] ||
            !$this->order['OrderPackages'][0]['Height']
        ) {
            session()->flash('error', "Please select a box");
            return;
        }

        $this->rates = [];
        // Reset Signature confirmation

        $this->order['requiresSignature'] = FALSE;

        // If this shipment is eligible for Fedex One rate, let's send that package type to ShipEngine
        $this->tryOneRate();
        $this->addSignature();
        $this->setCurrentWeight();
        if (
            !$this->order["ShippingDetails"]["EstShipWeight"]['Pounds'] &&
            !$this->order["ShippingDetails"]["EstShipWeight"]['Ounces']
        ) {
            session()->flash('error', "No weight detected");
            return;
        }

        if ($this->pin && $user = User::firstWhere(['pin' => $this->pin])) {
            logOrder($this->order['OrderID'], "$user->name bypassed the weight verification");
            logOrder('weight_failed', "$user->name bypassed the weight verification".' for order # '.$this->order['OrderID']);
            $this->failed = false;
            $this->pin = null;
        } else {
            $this->failed = false;
            $scaleWeight = ($this->order["ShippingDetails"]["EstShipWeight"]['Pounds'] * 16) + $this->order["ShippingDetails"]["EstShipWeight"]['Ounces'];

            $weight = optional($this->shippingStatistic)->weight;
            $leeway = $this->settings->weightLeeway / 100;

            if (
                $weight &&
                (($scaleWeight < $weight * (1 - $leeway))
                    || ($scaleWeight > $weight * (1 + $leeway)))
            ) {
                logOrder('weight_failed',  $this->order['OrderID'] . ' current weight ' . $scaleWeight . '   preset weight   ' . $weight);

                $this->failed = true;
                return;
            }
        }


        $rateResponse = $this->makeRateRequest();

        if ($this->order['isOneRate']) {
            $rateResponse['rate_response']['rates'][] = $this->getCheapestOneRate();
        }

        $this->setUpRates($rateResponse);

        if (is_array($this->rates) && count($this->rates)) {
            $this->selectRate();
        }

        if (
            $this->order['isPrime'] 
            &&
            ! in_array(auth()->user()->email,['<EMAIL>','<EMAIL>'])) {

            $rate =$this->rates[$this->selectedRate];
            if (
                !(
                    in_array(data_get($rate,'service_code'),['amazon_ups_2nd_day_air','amazon_ups_second_day_air_sat'])
                    &&
                    in_array(data_get($rate,'shipping_amount.amount'),[11.5,17.4])
                )
            ) {
                throw new \Exception('No One rate two day method avail for this prime order');

            }
        }

        if ($ship) {
            $this->ship();
        }
    }

    public function ship()
    {
        if (Shipment::where('order_id', $this->order['OrderID'])->where('status', 'completed')->get()->count()) {
            session()->flash('error', "If the shipping label for order #" . $this->order['OrderID'] . " did not print, please contact the shipping manager.");
            return;
        }

        if (!request()->cookie('label-printer') || !session('labelPrinterOnline')) {
            session()->flash('error', "No label printer selected, please select a printer");
            return;
        }

        $notDoneNotes =  optional(collect($this->order['notes']))->filter(function ($note) {
            return !$note['done'];
        })->keys()->map(function ($key) {
            return $key + 1;
        });;
        if (!!$notDoneNotes->count()) {
            session()->flash('error', "Please take care of " . Str::of('note')->plural($notDoneNotes->count()) . ' ' . $notDoneNotes->join(', '));
            return;
        }
        // if (config('app.env') == 'local') {
        //     session()->flash('error', 'Local dont want to ship');
        //     return;
        // }
        // return;

        $this->order['triedShipping'] = TRUE;

        if (!$this->label) {
            $this->label = $this->createLabel();

            if (!$this->label) {
                throw new \Exception("We were not able to generate a label", 1);
            }
        }
        // $this->order['errors'] = [];

        $rate = $this->rates[$this->selectedRate];
        $ratePrice = data_get($rate, 'shipping_amount.amount')
            + data_get($rate, 'insurance_amount.amount')
            + data_get($rate, 'confirmation_amount.amount')
            + data_get($rate, 'other_amount.amount');
        // if ($ratePrice != $this->label['shipment_cost']['amount']) {
        //     sendAdminMail("Order Id $this->order['OrderID'], Rate price was $this->order['ratePrice'], and Shipment price was $this->label['shipment_cost']['amount']");
        // }

        $this->saveShipment();

        if ($this->printLabel($this->order['OrderID'], $this->label['label_id'])) {
            logOrder($this->order['OrderID'],'line 479 sent to sc '.now());
            if (data_get($this->order, 'invoice')) {
                (new FabbleAPI)->shipOrder($this->label, $this->order['OrderDetails']['OrderSourceOrderId'], $this->order['OrderID'], $this->rates[$this->selectedRate]['estimated_delivery_date']);
            }
            $this->sendTrackingToSellerCloud();
            $this->resetOrder();
            $this->countOrders();
            return;
        } else {
            logOrder($this->order['OrderID'],'line 488 sent to sc '.now());
            throw new \Exception("Cant print label", 1);
        }
        logOrder($this->order['OrderID'],'ship.php line 491 '.now());

        return;


        if (!array_key_exists('shipment', $this->order) || !$this->order['shipment']) {
            $this->order['shipment'] = $this->saveShipment();
        }

        if (!data_get($this->order, 'printJob')) {
            // if (!array_key_exists('printJob', $this->order) || !$this->order['printJob']) {
            if (!$this->order['printJob'] = $this->printLabel($this->order['OrderID'], $this->label['label_id'])) {
                //notify
                return;
            }
        }

        if (!data_get($this->order, 'scResponse')) {
            // if (!array_key_exists('scResponse', $this->order) || !$this->order['scResponse']) {
            $this->order['scResponse'] = $this->sendTrackingToSellerCloud();
        }

        if (!data_get($this->order, 'emptySortbox')) {
            // if (!array_key_exists('emptySortbox', $this->order) || !$this->order['emptySortbox']) {
            $this->order['emptySortbox'] = $this->emptySortbox();
        }

        session()->put('ship_order', $this->order);

        if (
            data_get($this->order, 'shipment') &&
            data_get($this->order, 'scResponse') &&
            data_get($this->order, 'emptySortbox')
            // array_key_exists('shipment', $this->order) && $this->order['shipment'] &&
            // // array_key_exists('printJob', $this->order) && $this->order['printJob'] &&
            // array_key_exists('scResponse', $this->order) && $this->order['scResponse'] &&
            // array_key_exists('emptySortbox', $this->order) && $this->order['emptySortbox']
        ) {
            $this->resetOrder();
        }
    }

    public function getOrder(string $type, string $ID)
    {
        session()->put('orderType', $type);
        // $this->status = "Getting your order from SellerCloud...";
        // $this->statusType = "info";
        session()->flash('message', "Getting your order from SellerCloud...");
        $this->rates = [];
        $sellerCloudAPI = new SellerCloudAPI;
        $sellerCloudLightwallAPI = new SellerCloudLightwallAPI;

        try {
            if ($type == 'orderId') {
                $userId = cache($ID);
                $name = optional(User::find($userId))->name;
                if ($userId && $userId != auth()->id()) {
                    session()->flash('error', "$name loaded the order already");
                    return;
                };
                cache([$ID => auth()->id()], 5 * 60);
                $this->order = $sellerCloudAPI->getOrder($ID);
            } elseif ($type == 'sku') {
                if (!$this->picklist) {
                    throw new \Exception("You must define a picklist to search by SKU", 1);
                }

                $orders = $sellerCloudLightwallAPI
                    ->getOrdersByBarcode($this->picklist['id'], $ID);

                $orderId = data_get(collect($orders)
                    ->filter(function ($order) {
                        return data_get($order, 'totalUnits') == 1;
                    })->first(), 'orderId');

                if ($orderId) {
                    $this->order = $sellerCloudAPI->getOrder($orderId);
                } else {
                    throw new \Exception("No matching items found", 1);
                }

                // if($orders && count($orders['Items']) && array_key_exists('ID', $orders['Items'][0])) {
                //     $this->order = $sellerCloudAPI->getOrder($orders['Items'][0]['ID']);
                // } else {
                //     throw new \Exception("Sorry, we couldn't find your order", 1);
                // }
            } elseif ($type == 'sortBox') {
                $sortbox = $sellerCloudLightwallAPI->getSortbox($ID);

                $this->sortboxId = $sortbox['id'];

                if ($sortbox['status'] != 'ContentsReadyToShip') {
                    throw new \Exception("Sortbox status should be 'ContentsReadyToShip', Current status is " . $sortbox['status'], 1);
                }

                $this->sortboxId = $sortbox['id'];
                $order = $sellerCloudLightwallAPI->getOrderBySortbox($this->sortboxId);
                $userId = cache("$order[id]");
                $name = optional(User::find($userId))->name;
                if ($userId && $userId != auth()->id()) {
                    session()->flash('error', "$name loaded the order already");
                    return;
                };
                cache(["$order[id]" => auth()->id()], 5 * 60);
                $this->order = $sellerCloudAPI->getOrder($order['id']);
                $this->order['sortBoxId'] = $this->sortboxId;
                $this->order['sortBoxCRC'] = $ID;
            }

            if (is_array($this->order)) {
                // $this->status = "Found your order";
                // $this->statusType = "success";
                session()->flash('message', 'Found your order.');
                $this->order['OrderPackages'][0]['Length'] = 0;
                $this->order['OrderPackages'][0]['Width'] = 0;
                $this->order['OrderPackages'][0]['Height'] = 0;
                $this->order["ShippingDetails"]["EstShipWeight"]['Pounds'] = 0;
                $this->order["ShippingDetails"]["EstShipWeight"]['Ounces'] = 0;

                $this->skus = collect($this->order['OrderItems'])
                    ->groupBy('ProductID')
                    ->map(function ($item, $key) {
                        return $key . '_' . $item->sum('Qty');
                    })
                    ->join('__');

                session()->put('ship_skus', $this->skus);

                $this->shippingStatistic = ShippingStatistics::where('skus', $this->skus)->first();
                session()->put('ship_shippingStatistic', $this->shippingStatistic);

                if (!!optional($this->shippingStatistic)->box_id) {
                    $this->selectedShippingBox = $this->shippingStatistic->box_id;

                    $this->order['OrderPackages'][0]['Length'] = $this->shippingStatistic['length'];
                    $this->order['OrderPackages'][0]['Width'] = $this->shippingStatistic['width'];
                    $this->order['OrderPackages'][0]['Height'] = $this->shippingStatistic['height'];

                    session()->put('ship_selectedShippingBox', $this->selectedShippingBox);
                }

                $this->order['notes'] =  $sellerCloudAPI->getNotes($this->order['OrderID']);
                //$this->order['notes'] =  collect();

                $excluded = ['amazon_buy_shipping', 'stamps_com', 'usps'];
                $this->order['selectedCarriers'] = $this->carriers->whereNotIn('carrier_code', $excluded);
                // $this->selectedCarriers = $this->getCarriersByCode($excluded, TRUE);

                $this->order['isPrime'] = $this->order['OrderDetails']['OrderSubType'] == 7 || $this->order['OrderDetails']['OrderSubType'] == 10; // 7 is Prime, 10 is Prime business
                $this->order['requiresSignature'] = FALSE;
                $this->order['manualSignature'] = FALSE;
                $this->order['po_box'] = preg_match('/^(.*(?:(.*((p|post)[-.\s]*(o|off|office)[-.\s]*(box|bin)[-.\s]*)|.*((p |post)[-.\s]*(box|bin)[-.\s]*)))).*$/i', $this->order['ShippingAddress']['StreetLine1']) || preg_match('/^(.*(?:(.*((p|post)[-.\s]*(o|off|office)[-.\s]*(box|bin)[-.\s]*)|.*((p |post)[-.\s]*(box|bin)[-.\s]*)))).*$/i', $this->order['ShippingAddress']['StreetLine2']);
                $this->order['apo_fpo'] = ($this->order['ShippingAddress']['CountryCode'] == 'US' && ($this->order['ShippingAddress']['StateCode'] == 'AA') || ($this->order['ShippingAddress']['StateCode'] == 'AP') || ($this->order['ShippingAddress']['StateCode'] == 'AE'));
                $this->order['international'] = ($this->countryCode() != 'US');
                if ($this->order['OrderDetails']['OrderSource'] == 6) { // website order
                    $response = (new FabbleAPI)->checkIfCanceled($this->order['OrderDetails']['OrderSourceOrderId'], $this->order['OrderID']);
                    $this->order['canceled'] = data_get($response, 'canceled');
                    $this->order['invoice'] = data_get($response, 'invoice');
                    $this->order['gift_cards'] = data_get($response, 'gift_cards');
                    $this->order['order_gift'] = data_get($response, 'order_gift');
                    $this->order['items_gift'] = data_get($response, 'items_gift');
                    if ($this->order['items_gift']) {
                        $this->order['sc_item_Info'] = data_get($sellerCloudAPI->getProductDetails(collect($this->order['items_gift'])->pluck('item_sku')->join(','), $this->order['OrderID']), 'Items');
                    }
                }
                $this->checkShippingMapping();
                $this->checkIfCanShip();
                $this->printInvoice();
                $this->setView();
                $this->setCurrentWeight();

                session()->put('ship_order', $this->order);
            } elseif (is_string($this->order)) {
                throw new \Exception("Error finding your order: " . $this->order, 1);
            }
        } catch (\Exception $e) {
            // $this->status = $e->getMessage();
            // $this->statusType = "error";
            report($e);
            session()->flash('message', $e->getMessage());
            logOrder(data_get($this->order, 'OrderID'), $e->getMessage());
            // Log::debug($e->getMessage());
        }
    }

    public function getPicklist($picklistId)
    {
        $sellerCloudLightwallAPI = new SellerCloudLightwallAPI;

        try {
            $picklist = $sellerCloudLightwallAPI->getPicklist($picklistId);

            if (is_array($picklist)) {
                session()->flash('message', 'Found your picklist.');
                $this->picklist = $picklist;
                session()->put('ship_picklist', $this->picklist);
                $this->emit('focus-on-field', 'sku');
            } elseif (is_string($picklist)) {
                throw new \Exception("Error finding your picklist: " . $picklist, 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            logOrder($this->order['OrderID'], $e->getMessage());
            // Log::debug($e->getMessage());
        }
    }

    public function updateWeight($weight)
    {
        if ($weight) {
            $lb = floor($weight);
            $oz = round(max(0, ($weight - $lb) * 16));

            // If the weight is under 1oz, but not 0, we round it up to 1oz
            if (!$lb && !$oz && $weight) {
                $oz = 1;
            }

            $this->order["ShippingDetails"]["EstShipWeight"]['Pounds'] = $lb;
            $this->order["ShippingDetails"]["EstShipWeight"]['Ounces'] = $oz;
        } else {
            $this->order["ShippingDetails"]["EstShipWeight"]['Pounds'] = 0;
            $this->order["ShippingDetails"]["EstShipWeight"]['Ounces'] = 0;
        }
    }

    public function changeServiceMapping()
    {
        $this->checkShippingMapping();
        $this->checkIfCanShip();

        session()->put('ship_order', $this->order);
    }

    public function changeServiceSpeed($value)
    {
        $service = SeService::find($this->service['service_id']);
        $service->service_mapping_id = $value;
        $service->save();

        sendAdminMail("New SE speed mapping by user: " . $this->user->email . ", se code: $service->service_code, mapped to speed ID: $value");

        // $this->needsService = array_diff($this->needsService, ['speed']);
        // session()->put('ship_needsService');
        $this->checkShippingMapping();
        $this->checkIfCanShip();
        session()->put('ship_order', $this->order);
    }

    public function resetOrder($manual = false)
    {

        $printedGiftcards =  optional(collect(data_get($this->order, 'gift_cards')))->filter(function ($giftCard) {
            return  data_get($giftCard, 'printed');
        })->count();

        if ($manual && !!$printedGiftcards && !isAdmin()) {
            session()->flash('error', 'Cant reset order after printing Gift Card');
            return;
        }

        if ($manual && data_get($this->order, 'printedInvoice')) {
            $this->confirmReset = true;
            return;
        }

        $this->order = NULL;
        $this->sortboxId = NULL;
        $this->rates = [];
        $this->selectedRate = NULL;
        $this->label = NULL;
        $this->selectedShippingBox = -1;
        $this->needsService = NULL;
        $this->service = NULL;
        $this->skus = NULL;
        $this->shippingStatistic = NULL;
        $this->confirmReset = false;
        $this->gettingRates = false;

        session()->forget([
            'ship_skus',
            'ship_label',
            'ship_order',
            'ship_rates',
            'ship_service',
            'ship_needsService',
            'ship_shippingStatistic',
            'ship_selectedRate',
            'ship_selectedShippingBox',
        ]);

        if ($this->picklist) {
            $this->emit('focus-on-field', 'sku');
        } else {
            $this->emit('focus-on-field', session()->has('orderType') ? session()->get('orderType') : NULL);
        }
    }

    public function resetPicklist()
    {
        $this->picklist = NULL;
        session()->forget('ship_picklist');
        $this->emit('focus-on-field', 'picklistId');
    }

    public function resetRate()
    {
        $this->rates = [];
        $this->selectedRate = NULL;
        session()->forget(['ship_rates', 'ship_selectedRate']);
    }

    // public function unship() {
    //     try {
    //         $shipEngineAPI = new ShipEngineAPI;
    //         $response = $shipEngineAPI->unship($this->label['label_id']);

    //         if(array_key_exists('approved', $response) && $response['approved']) {
    //             $sellerCloudAPI = new SellerCloudAPI;
    //             $scResponse = $sellerCloudAPI->unshipPackage($this->order['OrderID']);

    //             if(is_string($scResponse)) {
    //                 throw new \Exception($scResponse, 1);
    //             }
    //             $this->status = 'Approved: ' . $response['message'];
    //             $this->statusType = "success";
    //         } else {
    //             // $this->status = 'Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '';
    //             // $this->statusType = "error";
    //             // session()->flash('message', 'Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');

    //             Log::debug('Unship Denied: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');
    //         }
    //     } catch (\Exception $e) {
    //         session()->flash('message', $e->getMessage());
    //         Log::debug($e->getMessage());
    //     }
    // }

    private function setCurrentWeight()
    {
        if (!auth()->user()->useQZ) {
            $printNodeAPI = new PrintNodeAPI;
            // $scales = $printNodeAPI->getScales($this->user->ComputerId);
            $scales = $printNodeAPI->getScales(request()->cookie('computer'));
            // $scale = $scales[array_search($this->user->ScaleId, array_column($scales, 'productId'))];
            $scale = $scales[array_search(request()->cookie('scale'), array_column($scales, 'productId'))];
            $weight = $scale['measurement']['lb'] / 1000000000;
            $this->updateWeight($weight);
        }
    }

    private function checkShippingMapping()
    {
        $this->service = (array)DB::table('sc_shipping_services AS sc')
            ->leftJoin('services AS ss', function ($join) {
                $join->on('sc.service_id', '=', 'ss.id')->orOn('sc.service_mapping_id', '=', 'ss.service_mapping_id');
            })
            ->leftJoin('service_mappings', 'service_mappings.id', '=', 'ss.service_mapping_id')
            ->where('sc.sellercloud_name', '=', $this->order['ShippingDetails']['Service'])
            ->select('*', 'ss.service_mapping_id as se_service_mapping_id', 'ss.name as se_name')
            ->first();


        //          $services = DB::table('sc_shipping_services AS sc')
        //             ->leftJoin('services AS ss', 'sc.service_id', '=', 'ss.id')
        //             // ->leftJoin('service_mappings AS sm', 'sc.service_mapping_id', '=', 'sm.id')
        //             ->where('sc.sellercloud_name', '=', $scServiceName);

        // // // Get the ShipEngine service that is linked to this order's SC service
        // // $services = DB::table('services AS ss')
        // //                 ->join('sc_shipping_services', 'sc_shipping_services.service_id', '=', 'ss.id')
        // //                 ->where('sc_shipping_services.sellercloud_name', '=', $scServiceName);

        // // If this is locked shipping method, we will only use this exact method, otherwise, we are joining the services table again to get all services with the same or better shipping speed
        // if(!$this->order['ShippingDetails']['LockShippingMethod']) {
        //     $services->leftJoin('services AS s', function($join) {
        //                 $join->on('s.service_mapping_id', '<=', 'ss.service_mapping_id')->orOn('s.service_mapping_id', '<=', 'sc.service_mapping_id');
        //              })
        //              ->leftJoin('carriers', 'carriers.carrier_code', '=', 's.carrier_code')
        //              ->where('carriers.active', 1)
        //              ->where('s.active', 1);


        //     if($this->order['isPrime']) {
        //         $services->where(function ($query) {
        //             $query->where('s.use_for_prime_rush', 1)
        //                   ->orWhere('s.service_mapping_id', 4);
        //         });
        //     }
        // } else {
        //     $services->leftJoin('carriers', 'carriers.carrier_code', '=', 'ss.carrier_code')
        //          ->where('carriers.active', 1)
        //          ->where('ss.active', 1);
        // }

        // $this->order['services'] = $services->get();

        $this->needsService = FALSE;

        if (count($this->service) && !$this->service['se_service_mapping_id']) {
            $this->needsService = 'speed';
        }

        if (!count($this->service) || !$this->service['service_code']) {
            $this->needsService = 'mapping';
        }

        // if(count($this->service) && $this->service['service_code'] && $this->service['se_service_mapping_id']) {
        //     $this->needsService = [];
        // } elseif(count($this->service) && array_key_exists('service_code', $this->service) && $this->service['service_code']) {
        //     array_push($this->needsService, 'speed');
        // } else {
        //     array_push($this->needsService, 'mapping');
        // }

        session()->put('ship_needsService', $this->needsService);
        session()->put('ship_service', $this->service);
    }

    private function checkIfCanShip()
    {
        $this->order['canShip'] = TRUE;

        $this->order['previous_orders'] = Shipment::where('order_id', $this->order['OrderID'])
            ->where('voided', 0)
            ->get();

        if ($this->user->email != '<EMAIL>') { //TODO permissions
            if (array_key_exists('previous_orders', $this->order) && count($this->order['previous_orders'])) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "This order was already shipped. Cannot ship again.");
            }

            if (array_key_exists('canceled', $this->order) && data_get($this->order, 'canceled')) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "This order was canceled. Cannot ship.");
            }

            if (!$this->order['Statuses']['Confirmed']) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "Order status is not 'Confirmed'. Cannot ship.");
            }

            if ($this->order['Statuses']['OrderStatus'] != 2) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "Order status is not 'In Process'. Cannot ship.");
            }

            // if (($this->order['Statuses']['PaymentStatus'] != 30)) {
            if (($this->order['Statuses']['PaymentStatus'] != 30) && !$this->order['ShippingDetails']['AllowShippingWithoutPaymentValue']) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "Payment status is not 'Charged'. Cannot ship.");
            }

            if ($this->order['Statuses']['ShippingStatus'] === 3) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "Shipping status is 'Fully Shipped'. Cannot ship.");
            }

            if (config('app.env') == 'dev' || 'local') {
                return;
            }

            if ($this->order['Statuses']['PickingStatus'] != 2) {
                $this->order['canShip'] = FALSE;
                session()->flash('message', "Picking status is not 'Fully picked'. Cannot ship.");
            }
        }

        // If we still need to get a service mapping, we can't ship
        if ($this->needsService) {
            $this->order['canShip'] = FALSE;
            session()->flash('message', "Please select a shipping speed mapping to ship");
        }
    }

    private function selectRate()
    {
        // $this->status = "Choosing best rate...";
        // $this->statusType = "info";
        session()->flash('message', "Choosing best rate...");
        // rates are already sorted lowest to highest
        // Start by selecting the cheapest rate. Any other rules should happen after that. That way we always have a selected rate
        $this->selectCheapestRate();

        // if($this->order['SubTotal'] < 150) {
        //     // We will just take the cheapest rate

        // }

        // OrderSource 6 is website
        // if we have invoice then its fabble not photo4less
        if ($this->order['TotalInfo']['SubTotal'] >= 150 || ($this->order['OrderDetails']['OrderSource'] == 6 && data_get($this->order, 'invoice'))) {
            // Get the cheapest rate but exclude USPS first class
            $this->selectCheapestNoFirstClass();
        }

        if ($this->order['TotalInfo']['SubTotal'] >= 1000 && !$this->order['po_box'] && !$this->order['apo_fpo']) {
            $this->selectNonUSPSDHL();
        }

        // Add a Selected flag to the selected rate
        $this->rates[$this->selectedRate]['selected'] = TRUE;
        // $this->status = "Found best rate...";
        // $this->statusType = "success";
        session()->flash('message', "Found best rate...");
        session()->put('ship_selectedRate', $this->selectedRate);
    }

    private function selectCheapestRate()
    {
        // TODO calculate cheapest by smming up all amounts (confirmation, insurance, other and shipping)
        $this->selectedRate = reset($this->rates)['rate_id'];
        $this->rateReason = "the order total is less than $150, so we took the cheapest rate.";
    }

    private function selectCheapestNoFirstClass()
    {
        // take the first rate that is not USPS first class
        foreach ($this->rates as $rate) {
            if ($rate['service_code'] != "usps_first_class_mail" && $rate['service_code'] != "smartmail_parcels_expedited" && $rate['service_code'] != "smartmail_parcel_plus_expedited") {
                $this->selectedRate = $rate['rate_id'];
                $this->rateReason = "the order total is more than $150, so we took the cheapest rate excluding USPS first class and DHL expedited.";
                break;
            }
        }
    }

    private function selectNonUSPSDHL()
    {
        // take the first rate that is not USPS or DHL
        foreach ($this->rates as $rate) {
            if (!str_starts_with($rate['service_code'], 'usps_') && $rate['carrier_code'] != 'dhl_global_mail') {
                $this->selectedRate = $rate['rate_id'];
                $this->rateReason = "the order total is more than $1,000, so we took the cheapest rate excluding USPS";
                break;
            }
        }
    }

    private function saveShipment()
    {
        $shipment = new Shipment();
        $shipment->label_id = $this->label['label_id'];
        $shipment->order_id = $this->order['OrderID'];
        $shipment->user_id = $this->user->id;
        $shipment->external_order_id = $this->order['OrderDetails']['OrderSourceOrderId'];
        $shipment->status = $this->label['status'];
        $shipment->shipment_id = $this->label['shipment_id'];
        $shipment->ship_date = $this->label['ship_date'];
        $shipment->se_create_date = $this->label['created_at'];
        $shipment->shipment_cost = $this->label['shipment_cost']['amount'];
        $shipment->tracking_number = $this->label['tracking_number'];
        $shipment->carrier_id = $this->label['carrier_id'];
        $shipment->service_code = $this->label['service_code'] . (data_get($this->order, 'saturday_delivery') == true ? '_saturday' : '');
        $shipment->package_code = $this->label['package_code'];
        $shipment->voided = $this->label['voided'];
        $shipment->carrier_code = $this->label['carrier_code'];
        $shipment->pdf = $this->label['label_download']['pdf'];
        $shipment->zpl = $this->label['label_download']['zpl'];
        $shipment->weight = $this->label['packages'][0]['weight']['value'];
        $shipment->length =  $this->order['OrderPackages'][0]['Length'];
        $shipment->width =   $this->order['OrderPackages'][0]['Width'];
        $shipment->height =   $this->order['OrderPackages'][0]['Height'];
        $shipment->skus = $this->skus;
        $shipment->box_id = $this->selectedShippingBox > 0 ? $this->selectedShippingBox : 0;
        $shipment->sortbox_crc  = data_get($this->order, 'sortBoxCRC');
        $shipment->customs_form  = data_get($this->label, 'form_download.href');
        $shipment->env  = config('app.env');
        $shipment->estimated_delivery_date = data_get($this->rates[$this->selectedRate], 'estimated_delivery_date') ? now()->parse(data_get($this->rates[$this->selectedRate], 'estimated_delivery_date')) : null;

        $shipment->save();

        return $shipment;
    }

    public function printLabel($orderId, $labelId)
    {
        try {
            logOrder($orderId,'Print label function');
            logOrder($orderId,auth()->user()->useQZ);
            if (auth()->user()->useQZ) {
                $this->emit('printQZLabel', $this->label['label_download']['zpl']);

                return ['success' => TRUE];
            } else {
                $printNodeAPI = new PrintNodeAPI;
                logOrder($orderId, 'Sending to printer');
                $printJob = $printNodeAPI->print($this->label['label_download']['pdf'], "Order ID: $orderId, Label ID: $labelId", $orderId);
                logOrder($orderId, 'Sent to printer');
                session()->flash('message', "Sent to printer");

                logOrder($orderId, $printJob);

                if (!$printJob || $printJob['state'] != 'done') {
                    logOrder($orderId, 'didnt print label');
                    session()->flash('error', "If the shipping label for order #$orderId did not print, please contact the shipping manager.");
                    throw new \Exception("Unable to print", 1);
                }

                return true;
                // return $printJob;
            }
        } catch (\Exception $e) {
            report($e);
            logOrder($orderId, $e->getMessage());
            $failedApiRequest = FailedApiRequest::create([
                'order_id' => $orderId,
                'type' => 'PrintNode',
                'order' => collect($this->order)->only('OrderID', "OrderDetails", 'sortBoxId')->toArray(),
                'label' => $this->label,
                'printer_id' => request()->cookie('label-printer'),
                'sortbox_id' => $this->sortboxId,
                'error' => $e->getMessage(),
                'status' => 'Unresolved',

            ]);
            dispatch(function () use ($failedApiRequest) {

                $users = User::where('receive_notifications', true)->get(['name', 'email']);

                $data = [
                    'order_id' => $failedApiRequest->order_id,
                    'issue' => $failedApiRequest->error,
                    'shipper' => $failedApiRequest->shipment->user->name,
                    'link' => config('app.url') . "/issues?search=$failedApiRequest->order_id",
                ];
                $users->each(function ($user) use ($data) {

                    (new PrintNodeErrorEmail)
                        ->withData($data)
                        ->sendTo($user);
                });
            });
            report($e);
            logOrder($orderId, $e->getMessage());
            //     session()->flash('message', $e->getMessage());
            //     $this->resetOrder();

            //     Log::debug($e->getMessage());
            //     return FALSE;
        }
    }

    private function createLabel($dontUseSturday = false)
    {
        $shipEngineAPI = new ShipEngineAPI;

        if (array_key_exists($this->selectedRate, $this->rates)) {

            // DHL requires some confirmation, so if we are choosing a DHL rate, we add delivery confirmation. DHL does not charge extra for that. Other carriers do.
            if ($this->rates[$this->selectedRate]['carrier_code'] == 'dhl_global_mail' && $this->order['rateRequest']['shipment']['confirmation'] != 'signature') {
                $this->order['rateRequest']['shipment']['confirmation'] = "delivery";
            }

            if ($this->rates[$this->selectedRate]['carrier_code'] != 'amazon_buy_shipping') {
                $this->order['rateRequest']['shipment']['packages'][0]['label_messages'] = [
                    'reference1' => $this->order['OrderID'],
                    'reference2' => $this->order['OrderDetails']['OrderSourceOrderId']
                ];
            }

            // on thursady we want orders with 2 day onerate to be saturday delivery true
            if ((today()->isThursday()&& $this->order['isPrime'])||(!$dontUseSturday && today()->isThursday() && $this->rates[$this->selectedRate]['carrier_id'] == 'se-431659' && $this->rates[$this->selectedRate]['service_code'] == 'ups_2nd_day_air')) {
                $this->order['saturday_delivery'] = true;
                $this->order['rateRequest']['shipment']['advanced_options'] = [
                    'saturday_delivery' => 'true'

                ];
            }

            $this->order['rateRequest']['shipment']['service_code'] = $this->rates[$this->selectedRate]['service_code'];
            $this->order['rateRequest']['shipment']['carrier_id'] = $this->rates[$this->selectedRate]['carrier_id'];
            $this->order['ratePrice'] = data_get($this->rates[$this->selectedRate], 'calculated_price');

            if ($this->rates[$this->selectedRate]['carrier_id'] == 'se-398785') { // amazon shipping

                $current = null;
                $next = null;
                collect($this->rates)
                    ->each(function ($rate) use (&$current, &$next) {
                        if (!$current && $rate['carrier_id'] == 'se-398785') {
                            $current = $rate;
                        } else if ($current && !$next && $rate['carrier_id'] != 'se-398785') {
                            $next = $rate;
                        }
                    });


                $currentPrice = data_get($this->rates[$this->selectedRate], 'calculated_price');
                $nextPrice = data_get($next, 'calculated_price');


                \DB::insert('insert into amazon_shipments_reports (order_id,carrier, shipping_price, next_carrier, next_carrier_price,created_at,updated_at) values (?, ?,?, ?, ?,?,?)', [$this->order['OrderID'], data_get($this->rates[$this->selectedRate], 'service_code'), $currentPrice,  data_get($next, 'service_code') . ' ' . data_get($next, 'carrier_nickname'), $nextPrice, now(), now()]);
            }

            $response = $shipEngineAPI->createLabelFromScratch($this->order['rateRequest'], $this->order['OrderID']);

            if (!array_key_exists('errors', $response) || !count($response['errors'])) {
                if ($response['status'] == 'completed') {
                    return $response;
                }
                //  else {
                //     return FALSE;
                //     // throw new \Exception("We were not able to generate a label", 1);
                // }
            } else {
                if (str_contains(data_get($response, 'errors.0.message'), 'Saturday Delivery')) {
                    $this->order['saturday_delivery'] = false;
                    $this->order['rateRequest']['shipment']['advanced_options'] = [
                        'saturday_delivery' => 'false'

                    ];
                    return $this->createLabel(true);
                } else {
                    // return FALSE;
                    throw new \Exception('message : ' . data_get($response, 'errors.0.message') . ' Carrier: ' . data_get($response, 'errors.0.carrier_name'), 1);
                }
            }
        }
        return FALSE;
    }

    public function printQZLabel()
    {
    }

    private function sendTrackingToSellerCloud()
    {
        logOrder($this->order['OrderID'],'sent to sc '.now());
        logOrder($this->order['OrderID'], $this->order);
        $order = collect($this->order)->only('OrderID', "OrderDetails", 'sortBoxId', 'saturday_delivery')->toArray();
        $label = $this->label;
        $sortboxId = data_get($this->order, 'sortBoxId');

        SendOrderToSellerCloud::dispatch($order, $label, $sortboxId)->onQueue('sc');
        return;
        dispatch(function () use ($order, $label, $sortboxId) {
            $sellerCloudAPI = new SellerCloudAPI;
            $sellerCloudAPI->shipPackage($order, $label);
            $sellerCloudAPI->addCustomerServiceNote($order['OrderID'], 'Shipped by: ' . auth()->user()->email);

            $order = $sellerCloudAPI->getOrder($this->order['OrderID']);
            if (data_get($order, 'Statuses.ShippingStatus') == 3) {
                $this->emptySortbox($sortboxId, $order, $label);
            } else {
                FailedApiRequest::create([
                    'order_id' => $order['OrderID'],
                    'type' => 'SellerCloud',
                    'order' => $order,
                    'label' => $label,
                    // 'printer_id' => request()->cookie('label-printer'),
                    'error' =>  'Not marked as shipped on Sellercloud',
                    'status' => 'Unresolved',

                ]);
                logOrder($order['OrderID'], 'Not marked as shipped on Sellercloud');
                // Log::error('Order ' . $order['OrderID'] . ' seems to not have shipped');
            }
        });
    }

    public function emptySortbox($sortboxId, $order, $label)
    {
        if (array_key_exists('sortBoxId', $order)) {
            // if ($sortboxId) {
            // if (isset($this->sortboxId) && $this->sortboxId) {
            $sellerCloudLightwallAPI = new SellerCloudLightwallAPI;

            $wall_order_id = data_get(
                $sellerCloudLightwallAPI->getOrderBySortbox($sortboxId),
                'id'
            );
            if ($order['OrderID'] == $wall_order_id) {
                try {
                    $sellerCloudLightwallAPI->emptyBox($sortboxId, $order['OrderID']);
                } catch (\Exception $e) {
                    FailedApiRequest::firstOrCreate(
                        ['order_id' => data_get($order, 'OrderID')],
                        [
                            'type' => 'SkuBlox',
                            'order' => $order,
                            'label' => $label,
                            'sortbox_id' => $sortboxId,
                            // 'printer_id' => request()->cookie('label-printer'),
                            'error' => "Was unable to turn off Sortbox for order ". data_get($order, 'OrderID'),
                            'status' => 'Unresolved',
                        ]
                    );
                    logOrder(data_get($order, 'OrderID'), 'Sort box light was not closed');
                    logOrder(data_get($order, 'OrderID'), $e->getMessage());
                }

                $sortbox = $sellerCloudLightwallAPI->getSortboxById($sortboxId, $order['OrderID']);

                if ($sortbox['status'] != "Unassigned") {
                    FailedApiRequest::firstOrCreate(
                        ['order_id' => data_get($order, 'OrderID')],
                        [
                            'type' => 'SkuBlox',
                            'order' => $order,
                            'label' => $label,
                            'sortbox_id' => $sortboxId,
                            // 'printer_id' => request()->cookie('label-printer'),
                            'error' => "Was unable to turn off Sortbox {$sortbox['crc']}",
                            'status' => 'Unresolved',
                        ]
                    );

                    // FailedApiRequest::create([
                    //     'order_id' => $order['OrderID'],
                    //     'type' => 'SkuBlox',
                    //     'order' => $order,
                    //     'label' => $label,
                    //     'sortbox_id' => $sortboxId,
                    //     'error' =>  "Was unable to turn off Sortbox {$sortbox['crc']}",
                    //     'status' => 'Unresolved',
                    // ]);
                    logOrder(data_get($order, 'OrderID'), 'Sort box light was not closed');
                    // Log::error('Was unable to turn off Sortbox ' . $sortbox['crc']);
                } else {
                    optional(FailedApiRequest::where('order_id', $order['OrderID']))->update([
                        'status' => 'Resolved',
                        'resolved_by' => optional(auth()->user())->name,
                    ]);
                }
            };
        }
        // else {
        //     logOrder(data_get($order, 'OrderID'), 'No sortbox id passed along, Sort box light was not closed');

        //     FailedApiRequest::firstOrCreate(
        //         ['order_id' => data_get($order, 'OrderID')],
        //         [
        //             'type' => 'SkuBlox',
        //             'order' => $order,
        //             'label' => $label,
        //             'sortbox_id' => $sortboxId,
        //             'printer_id' => request()->cookie('label-printer'),
        //             'error' => 'Was unable to turn off Sortbox no ID was passed in request',
        //             'status' => 'Unresolved',
        //         ]
        //     );
        // }
    }

    public function validateAddress($address, $orderId)
    {
        $shpEngineApi = new ShipEngineAPI;

        $response = $shpEngineApi->validateAddress(
            [
                collect([
                    'address_line1' => $address['StreetLine1'],
                    'city_locality' => $address['City'],
                    'state_province' => $address['StateCode'],
                    'postal_code' => $address['PostalCode'],
                    'country_code' => $address['CountryCode']
                ])
            ],
            $orderId
        );
        return $response;
    }

    private function buildShipRequest($oneRate = FALSE)
    {
        $request = [];

        if (strlen($this->order['ShippingAddress']['StateCode']) > 2 && substr($this->order['ShippingAddress']['StateCode'], -1) == '.') {
            $this->order['ShippingAddress']['StateCode'] = substr($this->order['ShippingAddress']['StateCode'], 0, -1);
        }
        // $request['advanced_options'] = [
        //     'custom_field1' => $this->order['OrderID'],
        //     'custom_field2' => $this->order['OrderDetails']['OrderSourceOrderId'],
        //     'custom_field3' => Auth::id()
        // ];

        $validatedAddress = $this->validateAddress($this->order['ShippingAddress'], $this->order['OrderID']);

        $request['shipment'] = [ // Shipment should be a type
            'warehouse_id' => data_get($this->order, 'invoice') ? Setting::where('name', 'Fabble')->get()->first()->seWarehouse : $this->settings->seWarehouse,
            // 'validate_address' => 'validate_and_clean',
            'ship_date' => date('Y-m-d'),
            'ship_to' => [
                'name' => $this->order['ShippingAddress']['FirstName'] . ' ' . $this->order['ShippingAddress']['MiddleInitial'] . ' ' . $this->order['ShippingAddress']['LastName'],
                'address_line1' => $this->order['ShippingAddress']['StreetLine1'],
                'address_line2' => $this->order['ShippingAddress']['StreetLine2'],
                'city_locality' => $this->order['ShippingAddress']['City'],
                'state_province' => $this->order['ShippingAddress']['StateCode'],
                'postal_code' => $this->order['ShippingAddress']['PostalCode'],
                'phone' => $this->order['ShippingAddress']['PhoneNumber'],
                'country_code' => $this->countryCode(),
                'address_residential_indicator' => data_get($validatedAddress, '0.status') == 'verified' ? (data_get($validatedAddress, '0.matched_address.address_residential_indicator')) ?? 'yes' : 'yes',
            ],
            'packages' => [
                [
                    'weight' => [
                        'value' => (float)$this->order['ShippingDetails']['EstShipWeight']['Pounds'] * 16 + (float)$this->order['ShippingDetails']['EstShipWeight']['Ounces'],
                        'unit' => 'ounce'
                    ]
                ]
            ],
            // 'advanced_options' => [
            //     'saturday_delivery' => TRUE
            // ]
            // 'advanced_options' => [
            //     'custom_field1' => $this->order['OrderID'],
            //     'custom_field2' => $this->order['OrderDetails']['OrderSourceOrderId'],
            //     'custom_field3' => Auth::id()
            // ]
        ];

        if (!empty($this->selectedPackageTypes)) {
            $request['rate_options']['package_types'] = $this->selectedPackageTypes;
        }
        // else {
        if ($oneRate) {
            $dimensions = [
                'length' => 11.75,
                'width' => 0.5,
                'height' => 14.75,
                'unit' => 'inch'
            ];
        } else {
            $dimensions = [
                'length' => ($this->order['OrderPackages'][0]['Length'] ? $this->order['OrderPackages'][0]['Length'] : 1),
                'width' => ($this->order['OrderPackages'][0]['Width'] ? $this->order['OrderPackages'][0]['Width'] : 1),
                'height' => ($this->order['OrderPackages'][0]['Height'] ? $this->order['OrderPackages'][0]['Height'] : 1),
                'unit' => 'inch'
            ];
        }

        $request['shipment']['packages'][0]['dimensions'] = $dimensions;
        // }

        if ($this->order['requiresSignature'] || $this->order['manualSignature']) {
            $request['shipment']['confirmation'] = 'signature';
        } else {
            $request['shipment']['confirmation'] = 'none';
        }


        if ($this->order['OrderDetails']['OrderSource'] == 4) { // 4 is amazon channel
            // old if statement (only for prime shipments we were able to use amazon shipping and only wanted amazon carrier)
            if ($this->order['isPrime']) {
            $primeCarriers = $this->carriers->whereIn('carrier_code', 'amazon_buy_shipping');
            $this->order['selectedCarriers'] = $primeCarriers->toArray();
            }else {   
                $amazonCarriers = $this->carriers->whereIn('carrier_code', 'amazon_buy_shipping');
                $this->order['selectedCarriers'] = array_merge($this->order['selectedCarriers'], $amazonCarriers->toArray());
            }

            // if (count($primeCarriers)) {
            $request['shipment']['order_source_code'] = 'amazon';
            $request['shipment']['items'] = [];

            foreach ($this->order['OrderItems'] as $orderItem) {
                $request['shipment']['items'][] = (object) [
                    'quantity' => $orderItem['Qty'],
                    'name' => $orderItem['DisplayName'],
                    'external_order_id' => $this->order['OrderDetails']['OrderSourceOrderId'],
                    'external_order_item_id' => $orderItem['EBayItemID'],
                ];
            }
            // } else {
            //     throw new \Exception("No Amazon carriers found for this Prime order", 1);
            // }
        }
        // now in function createLabel()
        //  else {
        //     $request['shipment']['packages'][0]['label_messages'] = [
        //         'reference1' => $this->order['OrderID'],
        //         'reference2' => $this->order['OrderDetails']['OrderSourceOrderId']
        //     ];
        // }

        // if (order is international) {
        $request['shipment']['customs'] = [
            'contents' => 'merchandise',
            'non_delivery' => 'treat_as_abandoned',
            'customs_items' => $this->customItems(),
        ];
        $request['shipment']['tax_identifiers'] = [
            [
                'taxable_entity_type' => 'recipient',
                // 'identifier_type' => 'ein',
                // 'value' => '2134567890',
                'issuing_authority' => 'US'
            ]
        ];
        // }

        $request['rate_options'] = $this->getRateOptions();

        return $request;
    }

    public function countryCode()
    {
        // 50 states plus DC and army forces AA AE AP
        $us_state_abbrevs = [
            'AK', 'AL', 'AR', 'AZ',
            'CA', 'CO', 'CT', 'DC',
            'DE', 'FL', 'GA', 'HI',
            'IA', 'ID', 'IL', 'IN',
            'KS', 'KY', 'LA', 'MA',
            'MD', 'ME', 'MI', 'MN',
            'MO', 'MS', 'MT', 'NC',
            'ND', 'NE', 'NH', 'NJ',
            'NM', 'NV', 'NY', 'OH',
            'OK', 'OR', 'PA', 'RI',
            'SC', 'SD', 'TN', 'TX',
            'UT', 'VA', 'VT', 'WA',
            'WI', 'WV', 'WY', 'AA',
            'AE', 'AP'
        ];
        if ($this->order['ShippingAddress']['CountryCode'] == 'US' && !collect($us_state_abbrevs)->contains($this->order['ShippingAddress']['StateCode'])) {
            return $this->order['ShippingAddress']['StateCode'];
        }
        return  $this->order['ShippingAddress']['CountryCode'];
    }

    public function customItems()
    {
        return collect($this->order['OrderItems'])->map(function ($item) {
            return  [
                // todo 
                // update to real stuff
                'harmonized_tariff_code' => '',
                'country_of_manufacture' => 'US',
                'quantity' => $item['Qty'],
                'value' => [
                    'currency' => 'usd',
                    'amount' => $item['SitePrice'] > 0 ? $item['SitePrice'] : '1.00'
                ],
                'sku' => $item['ProductID'],
                // 'description' => $item['DisplayName']
                'description' => substr($item['DisplayName'], 0, 100)
            ];
        });
    }

    private function getRateOptions()
    {
        $response = [
            'carrier_ids' => [],
            'service_codes' => []
        ];

        $scServiceName = $this->order['ShippingDetails']['Service'];

        $services = DB::table('sc_shipping_services AS sc')
            ->leftJoin('services AS ss', 'sc.service_id', '=', 'ss.id')
            // ->leftJoin('service_mappings AS sm', 'sc.service_mapping_id', '=', 'sm.id')
            ->where('sc.sellercloud_name', '=', $scServiceName);

        // // Get the ShipEngine service that is linked to this order's SC service
        // $services = DB::table('services AS ss')
        //                 ->join('sc_shipping_services', 'sc_shipping_services.service_id', '=', 'ss.id')
        //                 ->where('sc_shipping_services.sellercloud_name', '=', $scServiceName);

        // If this is locked shipping method, we will only use this exact method, otherwise, we are joining the services table again to get all services with the same or better shipping speed
        if (!$this->order['ShippingDetails']['LockShippingMethod']) {
            $services->leftJoin('services AS s', function ($join) {
                $join->on('s.service_mapping_id', '<=', 'ss.service_mapping_id')->orOn('s.service_mapping_id', '<=', 'sc.service_mapping_id');
            })
                // ->leftJoin('carriers', 'carriers.carrier_code', '=', 's.carrier_code')
                // ->where('carriers.active', 1)
                // ->where('s.active', 1)
            ;

            $scService = ScService::where('sellercloud_name', $scServiceName)->first();
            $scMapping = $scService->service_mapping_id;
            $seMapping = optional($scService->seService)->service_mapping_id;




            // if ($this->order['isPrime']) {
            //     $services->where(function ($query) {
            //         $query->where('s.use_for_prime_rush', 1)
            //             ->orWhere('s.service_mapping_id', 4);
            //     });
            // }

            if ($this->order['po_box'] || $this->order['apo_fpo']) {

                // if po box or apo/fpo is mapped to 3 day method then we want to ship usps_priority_mail so we join it back
                if ((!!$scMapping && $scMapping == 3) || (!!$seMapping && $seMapping == 3)) {
                    $services->leftJoin('services AS se', function ($join) {
                        $join->on('se.service_mapping_id', '>=', 's.service_mapping_id')
                            ->where('se.service_code', '=',  'usps_priority_mail')
                            ->leftJoin('carriers', 'carriers.carrier_code', '=', 'se.carrier_code')
                            ->where('carriers.active', 1)
                            ->where('se.active', 1);
                    });
                } else {
                    $services
                        ->where('s.service_code',  'LIKE', '%' . 'usps' . '%')
                        ->leftJoin('carriers', 'carriers.carrier_code', '=', 's.carrier_code')
                        ->where('carriers.active', 1)
                        ->where('s.active', 1);
                }

                // the company doesnt want to use usps_priority_mail_express for 2 day orders so we filter it out
            } elseif (!$this->order['po_box'] && !$this->order['apo_fpo'] && ((!!$scMapping && $scMapping < 3) || (!!$seMapping && $seMapping < 3))) {
                $services->where('s.service_code', '!=', 'usps_priority_mail_express');
                $services->leftJoin('carriers', 'carriers.carrier_code', '=', 's.carrier_code')
                    ->where('carriers.active', 1)
                    ->where('s.active', 1);
            } else {
                $services->leftJoin('carriers', 'carriers.carrier_code', '=', 's.carrier_code')
                    ->where('carriers.active', 1)
                    ->where('s.active', 1);
            }
        } else {
            $services->leftJoin('carriers', 'carriers.carrier_code', '=', 'ss.carrier_code')
                ->where('carriers.active', 1)
                ->whereIn('carriers.carrier_id', array_column($this->order['selectedCarriers'], 'carrier_id'))
                ->where('ss.active', 1);
        }

        if($this->order['isPrime']){
            $this->order['services'] = DB::table('services')->where('services.active',true)->where('services.service_code', 'like', 'amazon_%')
        ->join('carriers', 'carriers.carrier_code', '=', 'services.carrier_code')
                ->where('carriers.active', 1)->get()->unique();
        }else{    
            $this->order['services'] = $services->get()->unique();
        }

        $carriersUsed = [];
        foreach ($this->order['services'] as $service) {
            $this->order['selectedCarriers'] = array_values($this->order['selectedCarriers']);

            if (in_array($service->carrier_id, array_column($this->order['selectedCarriers'], 'carrier_id'))) {
                $response['service_codes'][] = $service->service_code;
                $carriersUsed[] = $service->carrier_id;
            } else {
                $this->order['selectedCarriers'] = array_filter($this->order['selectedCarriers'], function ($carrier) use ($service) {
                    return $carrier != $service->carrier_id;
                });
            }
        }

        $response['carrier_ids'] = array_values(array_unique($carriersUsed)); //array_column($this->order['selectedCarriers'], 'carrier_id');

        return $response;
    }

    // private function getCarriersByCode(array $codes, $negate = FALSE) {
    //     $filteredCarriers = array_column(array_filter($this->carriers, function($carrier) use ($codes, $negate) {
    //         return ($negate && !in_array($carrier['carrier_code'], $codes)) || (!$negate && in_array($carrier['carrier_code'], $codes));
    //     }), 'carrier_id');

    //     return $filteredCarriers;
    // }

    private function setUpRates($responseObject)
    {
        if (!array_key_exists('rate_response', $responseObject)) {
            throw new \Exception("No rates found", 1);
        }

        $rates = $responseObject['rate_response']['rates'];

        // ShipEngine returns stamps.com rates even if we sent in service codes, so we will filter out all service codes that we didn't request
        foreach ($rates as $key => $rate) {
            if (!$this->order['services']->contains('service_code', $rate['service_code'])) {
                // if(!in_array($rate['service_code'], $this->order['services'])) {
                unset($rates[$key]);
            }
        }

        // First let's sort all rates lowest to highest
        $rates = $this->sortRates($rates);

        // Let's get an array of all rates with the rate ID as the key so we can easily access the selected rate
        $this->rates = array_column($rates, NULL, 'rate_id');

        // $this->rates = array_filter($this->rates, function($rate) use ($excludedServices) {
        //     return !in_array($rate['service_code'], $excludedServices);
        // });

        if (!is_array($this->rates) || !count($this->rates)) {
            $this->rates = "no_rate";
        }

        session()->put('ship_rates', $this->rates);
        session()->put('ship_order', $this->order);
    }

    public function toggleNoteDone($index)
    {
        $this->order['notes'][$index]['done'] = !$this->order['notes'][$index]['done'];
        session()->put('ship_order', $this->order);
    }

    public function closeModal()
    {
        $this->failed = false;
    }

    public function printInvoice($manual = false)
    {
        if (!$this->order['canShip'] && !$manual) {
            return;
       }

       $order = collect($this->order)->only('OrderID', 'invoice')->toArray();
       $invoicePrinter = request()->cookie('invoice-printer');
       $labelPrinter = request()->cookie('label-printer');
       $this->order['printedInvoice'] = true;
       session()->put('ship_order', $this->order);
       
       dispatch(function () use ($order ,$invoicePrinter ,$labelPrinter) {
            if (data_get($order,'invoice')) {

                (new PrintNodeAPI)->print(data_get($order,'invoice'), "Order ID: {$order['OrderID']}, Invoice", $order['OrderID'], $invoicePrinter);
            }else{

                $sellerCloudAPI = new SellerCloudAPI;
                $base64_pdf= $sellerCloudAPI->getInvoice($order['OrderID']);
                $printNodeAPI = new PrintNodeAPI;
                $printNodeAPI->print($base64_pdf, "Order ID: {$order['OrderID']}", $order['OrderID'], $labelPrinter ,'pdf_base64');
            }
        })->onQueue('sc');
    }

    public function printGiftCard($gift_card, $index)
    {
        if (!request()->cookie('card-printer') || !session('cardPrinterOnline')) {
            session()->flash('error', "No card printer selected, please select a printer");
            return;
        }
        if (!request()->cookie('invoice-printer') || !session('invoicePrinterOnline')) {
            session()->flash('error', "No invoice printer selected, please select a printer");
            return;
        }

        $previous = $index - 1;
        if ($previous >= 0 && data_get($this->order['gift_cards']["$previous"], 'printed') != true) {
            session()->flash('error', "Cant print before completing the previous Gift Card ");
            return;
        }
        (new PrintNodeAPI)->print($gift_card['mail_card'], 'Gift Card', $this->order['OrderID'], request()->cookie('card-printer'));

        (new PrintNodeAPI)->print($gift_card['mail_paper'], 'Gift Card Paper', $this->order['OrderID'], request()->cookie('invoice-printer'));

        $this->order['gift_cards']["$index"]['printed'] = true;
        session()->put('ship_order', $this->order);
    }

    public function confirmUpc($gift, $value)
    {
        $sc_item = collect($this->order['sc_item_Info'])->filter(function ($item) use ($gift) {
            return data_get($item, 'ID') == $gift['item_sku'];
        })->values()->first();
        $index = collect($this->order['items_gift'])->search(function ($gift_item) use ($gift) {
            return $gift_item['item_sku'] == $gift['item_sku'];
        });

        $previous = $index - 1;
        if ($previous >= 0 && data_get($this->order['items_gift']["$previous"], 'done') != true) {
            $this->order['items_gift']["$index"]['error'] = 'Please complete the previous gift';
            return;
        }
        if ($previous >= 0 && data_get($this->order['items_gift']["$previous"], 'gift_note') && data_get($this->order['items_gift']["$previous"], 'printed') != true) {
            $this->order['items_gift']["$index"]['error'] = 'Please print the previous gift note';
            return;
        }
        if (data_get($sc_item, 'UPC') == $value) {
            $this->order['items_gift']["$index"]['confirmed'] = true;
        } else {
            $this->order['items_gift']["$index"]['error'] = 'Wrong Item';
        }
        session()->put('ship_order', $this->order);
    }

    public function printGiftNote($gift, $index, $type)
    {
        if (!request()->cookie('invoice-printer') || !session('invoicePrinterOnline')) {
            session()->flash('error', "No invoice printer selected, please select a printer");
            return;
        }
        //todo
        //add bin
        (new PrintNodeAPI)->print($gift['gift_note_pdf'], 'Gift Note', $this->order['OrderID'], request()->cookie('invoice-printer'));
        if ($type == 'item') {
            $this->order['items_gift']["$index"]['printed'] = true;
            session()->put('ship_order', $this->order);
            return;
        }
    }

    public function ratesAndShip()
    {
        $this->getRates(true);
    }

    public function flashLight($sortBoxid)
    {
        $sellercloudLightWall = new SellerCloudLightwallAPI;
        $sellercloudLightWall->flashLight($sortBoxid);
    }

    public function showDetails()
    {
        if ($this->pin && $user = User::firstWhere(['pin' => $this->pin])) {
           
            $this->simplifiedView = false;
            
            $this->pin = null;

        }
    }

    public function countOrders()
    {
        $this->count = Shipment::whereDate('created_at', today())->where('user_id', auth()->id())->count();
    }

    public function setView()
    {
        $this->simplifiedView = true;

        if (
            auth()->user()->detailed_view
            || !!collect(data_get($this->order, 'notes'))->count()
            || !!data_get($this->order, 'canceled')
            || !!data_get($this->order, 'gift_cards')
            || !!data_get($this->order, 'order_gift')
            || !!data_get($this->order, 'items_gift')
            || !!$this->needsService
        ) {
            $this->simplifiedView = false;
        }

    }


    function logAllRates() {
        try {
            $skus = collect($this->order['OrderItems'])
                ->groupBy('ProductID')
                ->map(function ($item, $key) {
                    return $key . '_' . $item->sum('Qty');
                })
                ->join('__');

            $serviceCodes = \DB::table('services')
            	->get()
                ->map(function ($item) {
                    return "$item->carrier_code: $item->service_code";
                })
                ->sort()
                ->values();

            $path = storage_path().'/logs/rates/'.now()->format('Y-m-d') . '.csv';
            config(['logging.channels.csv.path' => $path]);

            if(!\File::exists($path)) {
                Log::channel('csv')->info(
                    collect([
                        'OrderID',
                        'zone',
                        'zip',
                        'selected Rate',
                        'price',
                        'skus'
                    ])
                        ->merge($serviceCodes)
                        ->join(',')
                );
            }

            $array = collect()->times($serviceCodes->count())->map(function () { return ''; })->toArray();

            collect($this->allRates)
                ->each(function ($rate) use ($serviceCodes, &$array) {
                    $key = $rate['carrier_code'] . ': ' . $rate['service_code'];
                    $array[$serviceCodes->search($key)] = $rate['shipping_amount']['amount'];
                });

            $currentRate = $this->rates[$this->selectedRate];
            Log::channel('csv')->info(
                    collect([
                        $this->order['OrderID'],
                        $currentRate['zone'],
                        data_get($this->order, 'ShippingAddress.PostalCode'),
                        $currentRate['carrier_code'] . ': '. $currentRate['service_code'],
                        $currentRate['calculated_price'],
                        $skus
                    ])
                    ->merge($array)
                    ->join(',')
            );
        } catch (\Exception $e) {
            report($e);
            logOrder($this->order['OrderID'], $e->getMessage());
        }
    }
}

// class Order {
//     public string $name;
// }

// class Address {
//     public string $name;
//     public string $company_name = '';
//     public string $address_line1;
//     public string $city_locality;
//     public string $state_province;
//     public string $postal_code;
//     public string $country_code;
//     public string $phone = '';
// }
