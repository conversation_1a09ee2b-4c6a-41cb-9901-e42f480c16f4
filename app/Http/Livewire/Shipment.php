<?php

namespace App\Http\Livewire;

use App\Library\Services\FabbleAPI;
use Livewire\Component;
use App\Library\Services\PrintNodeAPI;
use App\Library\Services\ShipEngineAPI;
use App\Library\Services\SellerCloudAPI;
use App\Models\FailedApiRequest;
use App\Models\Shipment as ModelsShipment;
use Log;

class Shipment extends Component
{
    public $shipment;

    public function render()
    {
        return view('livewire.shipment');
    }

    public function mount()
    {
        if (!$this->shipment instanceof ModelsShipment ) {
            $this->shipment = ModelsShipment::find($this->shipment['id']);
        }
    }

    public function unship($labelId, $orderId, $shipmentId)
    {
        $shipment = ModelsShipment::find($shipmentId);
        try {
            $shipEngineAPI = new ShipEngineAPI;
            $response = $shipEngineAPI->unship($labelId, $orderId);

            if (array_key_exists('approved', $response) && $response['approved']) {
                (new FabbleAPI)->unship($shipment->external_order_id, $orderId);
                $sellerCloudAPI = new SellerCloudAPI;

                $scResponse = $sellerCloudAPI->unshipPackage($orderId);

                $sellerCloudAPI->addCustomerServiceNote($orderId, 'Unshipped by: ' . auth()->user()->email);

                if (is_string($scResponse)) {
                    throw new \Exception($scResponse, 1);
                }
                $this->status = 'Approved: ' . $response['message'];
                $this->statusType = "success";

                $shipment = ModelsShipment::where('label_id', $labelId)
                    ->update(['voided' => 1, 'status' => 'voided']);
                logOrder($orderId, ModelsShipment::where('order_id', $orderId)->first()->status);
            } else {
                $this->status = 'Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '';
                $this->statusType = "error";
                session()->flash('message', 'Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');

                logOrder($orderId, 'Unship Denied: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');
                // Log::debug('Unship Denied: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            logOrder($orderId, $e->getMessage());
            // Log::debug($e->getMessage());
        }
    }

    public function print($url, $title)
    {
        $printNodeAPI = new PrintNodeAPI;
        $printJob = $printNodeAPI->print($url, $title, data_get($this, 'shipment.order_id'));

        if (!$printJob || $printJob['state'] != 'done') {
            throw new \Exception("Unable to print", 1);
        }

        session()->flash('message', "Sent to printer");
        return $printJob;
    }
}
