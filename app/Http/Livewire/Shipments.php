<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Library\Services\QzAPI;
use App\Models\Carrier;
use App\Models\Service;
use App\Models\User;
use App\Models\Shipment;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Log;
use Livewire\WithPagination;

class Shipments extends Component
{
    use WithPagination;
    use AuthorizesRequests;

    public $labels = [];
    public $users = [];
    public $statuses = [];
    public $carriers;
    public $services;
    public $columns = ['Order', 'Label', 'Date', 'Status', 'Tracking', ''];
    public $loading_message = "";
    public $perPage = 25;

    public $user;
    public $search;
    public $status;
    public $dates;
    public $carrier;
    public $service;

    protected $queryString = ['status', 'search' => ['except' => ''], 'dates' => ['except' => ''], 'carrier' => ['except' => ''], 'service' => ['except' => ''], 'user' => ['except' => ''],];

    public function render()
    {
        $shipments = $this->getShipments();

        return view('livewire.shipments', [
            'shipments' => $shipments
        ]);
    }

    public function mount()
    {
        $user = auth()->user();

        $this->users = User::all();
        $this->user = isAdmin() ? null : $user->id;

        $this->statuses = $this->getStatuses();
        $this->carriers = $this->getCarriers();
        $this->services = $this->getServices();
    }

    // public function updatingfilter()
    // {
    //     $this->resetPage();
    // }

    public function resetFilter()
    {
        $this->user = null;
        $this->search = null;
        $this->status = null;
        $this->dates = null;
        $this->carrier = null;
        $this->service = null;
    }
    public function updatedcarrier($value)
    {
        $this->service = null;
        $this->services = $this->getServices();
    }

    public function unship($labelId, $orderId)
    {
        try {
            $shipEngineAPI = new ShipEngineAPI;
            $response = $shipEngineAPI->unship($labelId, $orderId);

            if (array_key_exists('approved', $response) && $response['approved']) {
                $sellerCloudAPI = new SellerCloudAPI;
                $scResponse = $sellerCloudAPI->unshipPackage($orderId);

                if (is_string($scResponse)) {
                    throw new \Exception($scResponse, 1);
                }
                $this->status = 'Approved: ' . $response['message'];
                $this->statusType = "success";

                $shipment = Shipment::where('label_id', $labelId)
                    ->update(['voided' => 1, 'status' => 'voided']);
            } else {
                $this->status = 'Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '';
                $this->statusType = "error";
                session()->flash('message', 'Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');

                logOrder($orderId, 'Unship Denied: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');
                // Log::debug('Unship Denied: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '');
            }
        } catch (\Exception $e) {
            session()->flash('message', $e->getMessage());
            logOrder($orderId, $e->getMessage());
            // Log::debug($e->getMessage());
        }
    }

    private function getShipments()
    {
        $search = $this->search;
        $dates = $this->dates;

        $shipments = Shipment::latest();

        if (!empty($dates)) {
            $array = explode(' to ', $dates);
            $startDate = $array[0];
            $endDate = data_get($array, '1') ?? $array[0];
            $shipments->whereBetween('created_at', [now()->parse($startDate), now()->parse($endDate)]);
        }

        if (!empty($this->carrier)) {
            $shipments->where('carrier_id', $this->carrier);
        }

        if (!empty($this->service)) {
            $shipments->where('service_code', $this->service);
        }

        if (!empty($this->user)) {
            $shipments->where('user_id', $this->user);
        }

        if (!empty($this->status)) {
            $shipments->where('status', $this->status);
        }

        if (!empty($search)) {
            $shipments->where(function ($query) use ($search) {
                return $query->where('order_id', 'like', "%" . $search . "%")
                    ->orWhere('label_id', 'like', "%" . $search . "%")
                    ->orWhere('external_order_id', 'like', "%" . $search . "%")
                    ->orWhere('shipment_id', 'like', "%" . $search . "%")
                    ->orWhere('shipment_cost', 'like', "%" . $search . "%")
                    ->orWhere('tracking_number', 'like', "%" . $search . "%")
                    ->orWhere('carrier_id', 'like', "%" . $search . "%")
                    ->orWhere('service_code', 'like', "%" . $search . "%")
                    ->orWhere('carrier_code', 'like', "%" . $search . "%")
                    ->orWhere('zpl', 'like', "%" . $search . "%")
                    ->orWhere('pdf', 'like', "%" . $search . "%")
                    ->orWhereHas('user', function ($q) use ($search) {
                        $q->where('name', 'like', '%' . $search . '%');
                    });
            });
        }

        $shipments = $shipments->with('user')->paginate($this->perPage);

        foreach ($shipments as &$shipment) {
            if ($shipment->user) {
                $words = explode(' ', $shipment->user->name);
                $shipment->user->initials = strtoupper(substr($words[0], 0, 1) . substr(end($words), 0, 1));
            }

            $carrier = Carrier::where('carrier_id', $shipment->carrier_id)->first();
            if (str_contains($carrier->friendly_name, 'UPS')) {
                $shipment->carrier = "$carrier->nickname";
            } else {
                $shipment->carrier = "$carrier->friendly_name";
            }
        }

        return $shipments;
    }

    private function getStatuses()
    {
        return Shipment::groupBy('status')->pluck('status')->filter()->toArray();
    }

    public function getCarriers()
    {
        return Carrier::get(['carrier_id', 'nickname', 'friendly_name'])->toArray();
        return Shipment::groupBy('carrier_code')->pluck('carrier_code')->filter()->toArray();
    }


    public function getServices()
    {
        return  Shipment::whereIn('carrier_id', $this->carrier ? [$this->carrier] : collect($this->carriers)->pluck('carrier_id'))->groupBy('service_code')->pluck('service_code')->unique()->filter()->toArray();
        $services = collect(Shipment::groupBy('service_code')->pluck('service_code'))->filter();
        $new = $this->carrier ?  $services->filter(function ($service) {
            return !!Service::where('service_code', $service)->where('carrier_code', $this->carrier)->count();
        })->values() : $services;
        return $new->toArray();
    }
}
