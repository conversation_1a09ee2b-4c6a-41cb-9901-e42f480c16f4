<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Hash;

class UserListing extends Component
{
    public $user;
    public $rawPassword;

    public function render()
    {
        return view('livewire.user-listing');
    }

    public function changeStatus() {
        if($this->user->status == "active") {
            $this->user->status = "inactive";
        } else {
            $this->user->status = "active";
        }

        $this->user->save();
    }

    public function updatePassword() {
        $this->user->password = Hash::make($this->rawPassword);
        $saved = $this->user->save();
        $this->rawPassword = NULL;

        session()->flash('message', $saved == 1 ? 'User saved' : 'Error saving user');
    }
}
