<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Library\Services\PrintNodeAPI;
use App\Library\Services\QzAPI;
use App\Models\Role;
use App\Models\ShippingBox;
use App\Models\User;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Hash;

class UserPage extends Component
{
    private $printNodeKeys = [
        // 'WW_gGpUxVlYfldbAmrR7J3eD5HqURGfJCHzkbGZPzQ0',
        'NMJWBTjRyTY3t1Hbt4ngjx_dgh45jz4C12szOCBkCBg',
        // 'qcrEdSZkcyOF1iuCM8e0pkJwDyBNfs9DFEE1tLcA9dg',
        // 'TTaaYnNBPg5Qe23-SkJIrihp6NByHao7i2OvIh-NN8E'
    ];

    protected $rules = [
        'user.name' => '',
        'user.email' => '',
        'user.SellerCloudUsername' => '',
        'user.SellerCloudPassword' => '',
        'user.receive_notifications' => '',
        'user.detailed_view' => '',
        'user.pin' => '',
        'user.role_id' => '',
    ];

    public $user;
    public $userId;
    public $rawPassword;

    public $printNodeAccounts = [];
    public $selectedPrintNodeAccount;

    public $computers = [];
    public $selectedComputer;

    public $printers = [];
    public $selectedLabelPrinter;
    public $selectedInvoicePrinter;
    public $selectedCardPrinter;

    public $scales = [];
    public $selectedScale;

    public $useQZ;
    public $QZPrinters = [];
    public $selectedQZPrinter;
    public $QZDevices = [];
    public $selectedQZScale;
    public $QZInterfaces = [];
    public $selectedQZInterface;
    public $QZEndpoints = [];
    public $selectedQZEndpoint;

    public $shippingBoxes = [];
    public $selectedShippingBoxes = [];
    public $roles;

    public function render()
    {
        return view('livewire.user-page');
    }

    public function mount()
    {
        request()->cookie('label-printer') ?? session()->flash('error', "No label printer selected, please select a printer");
        $this->roles = Role::all();
        if ($this->userId == 'new') {
            $this->user = new user;
        } elseif ($this->userId) {
            $this->user = User::find($this->userId);

            if (!$this->user) {
                abort(404);
            }
        } else {
            $this->user = auth()->user();
        }
    }

    public function loadData()
    {
        $this->setShippingBoxes();
        $this->getAllPrintNodeAccounts();
        $this->setDevices();
        $this->getDevices();
    }

    public function saveUser()
    {
        if (auth()->user()->role_id != 1 && auth()->user()->role_id != 3) {
            session()->flash('error', "Please contact manager to update a profile");
            return;
        }

        if ($this->userId == 'new') {
            $this->user->status = 'active';
        }

        if ($this->rawPassword) {
            $this->user->password = Hash::make($this->rawPassword);
        }


        try {
            $this->validate([
                'user.name' => 'required',
                'user.password' => 'required',
                'user.email' => 'email|required|unique:users,email,' . $this->user->id,
                'user.pin' => 'nullable|min:4|unique:users,pin,' . $this->user->id,
                'user.role_id' => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors =   collect($e->errors())->flatten()->join(' ');
            session()->flash('error', $errors);

            throw $e;
        }

        $saved = $this->user->save();

        $saved ?  session()->flash('message',  'User saved') :  session()->flash('error', 'Error saving User');
    }

    public function updatedSelectedShippingBoxes()
    {
        // $this->user->shipping_boxes()->sync([]);
        $this->user->shipping_boxes()->sync($this->selectedShippingBoxes);
    }

    public function updatedselectedPrintNodeAccount($value)
    {
        $this->user->PrintNodeApiKey = $value;
        $this->user->PrinterId = NULL;
        $this->user->ComputerId = NULL;
        $this->user->ScaleId = NULL;
        $this->user->save();

        $this->getDevices();
    }

    public function updatedselectedComputer($value)
    {
        // $this->user->PrinterId = NULL;
        // $this->user->ScaleId = NULL;
        Cookie::queue(Cookie::forget('scale'));
        Cookie::queue(Cookie::forget('label-printer'));
        Cookie::queue(Cookie::forget('invoice-printer'));
        Cookie::queue(Cookie::forget('card-printer'));
        Cookie::queue('computer', $value, 5 * 365 * 24 * 60);

        // $this->user->ComputerId = $value;
        // $this->user->save();

        $this->getDevices();
    }

    public function updatingselectedLabelPrinter($value)
    {
        Cookie::queue('label-printer', $value, 5 * 365 * 24 * 60);
        $printNodeApi = new PrintNodeAPI;
        $labelPrinter = $printNodeApi->getPrinter($value);
        session()->put('labelPrinterOnline', data_get($labelPrinter,'0.state') === 'online');
        // $this->user->PrinterId = $value;
        // $this->user->save();
    }

    public function updatingselectedInvoicePrinter($value)
    {
        Cookie::queue('invoice-printer', $value, 5 * 365 * 24 * 60);
        $printNodeApi = new PrintNodeAPI;
        $invoicePrinter = $printNodeApi->getPrinter($value);
        session()->put('invoicePrinterOnline', data_get($invoicePrinter,'0.state') === 'online');
    
        // $this->user->PrinterId = $value;
        // $this->user->save();
    }
    public function updatingSelectedCardPrinter($value)
    {
        Cookie::queue('card-printer', $value, 5 * 365 * 24 * 60);
        $printNodeApi = new PrintNodeAPI;
        $cardPrinter = $printNodeApi->getPrinter($value);
        session()->put('cardPrinterOnline', data_get($cardPrinter,'0.state') === 'online');    
    
        // $this->user->PrinterId = $value;
        // $this->user->save();
    }

    public function updatingselectedScale($value)
    {
        Cookie::queue('scale', $value, 5 * 365 * 24 * 60);
        // $this->user->ScaleId = $value;
        // $this->user->save();
    }

    public function updatingselectedQZPrinter($value)
    {
        $this->user->QZPrinter = $value;
        $this->user->save();
    }

    public function updateScale($value)
    {
        $this->selectedQZScale = $value;
        $device = json_decode($value);

        if ($device) {
            $this->user->QZScaleVendorId = $device->vendorId;
            $this->user->QZScaleProductId = $device->productId;
        } else {
            $this->user->QZScaleVendorId = NULL;
            $this->user->QZScaleProductId = NULL;
        }
        $this->user->save();
        $this->emit('deviceUpdated');
    }

    public function updatingselectedQZInterface($value)
    {
        $this->user->QZInterface = $value;
        $this->user->save();
        $this->emit('interfaceUpdated');
    }

    public function updatingselectedQZEndpoint($value)
    {
        $this->user->QZEndpoint = $value;
        $this->user->save();
    }

    public function updatinguseQZ($value)
    {
        Cookie::queue('useQZ', $value, 5 * 365 * 24 * 60);

        // $this->user->useQZ = $value;
        // $this->user->save();
    }

    public function printTestLabel()
    {
        if (!request()->cookie('label-printer') || !session('labelPrinterOnline')) {
            session()->flash('error', "No label printer selected, please select a printer");
            return;
        }

        $printNodeAPI = new PrintNodeAPI;
        $printNodeAPI->print('https://api.printnode.com/static/test/pdf/test.pdf', 'Ship P4L Label Test Label', null, request()->cookie('label-printer'));
    }

    public function printTestInvoice()
    {
        if (!request()->cookie('invoice-printer') || !session('invoicePrinterOnline')) {
            session()->flash('error', "No invoice printer selected, please select a printer");
            return;
        }

        $printNodeAPI = new PrintNodeAPI;
        $printNodeAPI->print('https://api.printnode.com/static/test/pdf/4x6_2_up_ol145.pdf', 'Ship P4L Invoice Test', null, request()->cookie('invoice-printer'));
    }

    public function printTestCard()
    {
        if (!request()->cookie('card-printer') || !session('cardPrinterOnline')) {
            session()->flash('error', "No card printer selected, please select a printer");
            return;
        }

        $printNodeAPI = new PrintNodeAPI;
        $printNodeAPI->print('https://api.printnode.com/static/test/pdf/test.pdf', 'Ship P4L Card Test', null, request()->cookie('card-printer'));
    }

    private function setShippingBoxes()
    {
        $this->shippingBoxes = ShippingBox::all();

        $this->selectedShippingBoxes = $this->user->shipping_boxes()->allRelatedIds()->toArray();
    }

    private function getAllPrintNodeAccounts()
    {
        foreach ($this->printNodeKeys as $key) {
            $printNodeAPI = new PrintNodeAPI($key);
            $account = $printNodeAPI->whoami();
            $account['apiKey'] = $key;

            if ($account['state'] == "active") {
                $this->printNodeAccounts[] = $account;
            }
        }
    }

    private function setDevices()
    {
        $this->selectedPrintNodeAccount = $this->user->PrintNodeApiKey;
        // $this->selectedComputer = $this->user->ComputerId;
        $this->selectedComputer = request()->cookie('computer');
        // $this->selectedLabelPrinter = $this->user->PrinterId;
        $this->selectedLabelPrinter = request()->cookie('label-printer');
        $this->selectedInvoicePrinter = request()->cookie('invoice-printer');
        $this->selectedCardPrinter = request()->cookie('card-printer');
        // $this->selectedScale = $this->user->SelectedScale;
        $this->selectedScale = request()->cookie('scale');
        $this->selectedQZPrinter = $this->user->QZPrinter;
        $this->selectedQZEndpoint = $this->user->QZEndpoint;
        $this->selectedQZInterface = $this->user->QZInterface;
        // $this->useQZ = $this->user->useQZ;
        $this->useQZ = request()->cookie('useQZ');


        $scale = (object) [
            'vendorId' => $this->user->QZScaleVendorId,
            'productId' =>  $this->user->QZScaleProductId
        ];
        $this->selectedQZScale = json_encode($scale);
    }

    private function getDevices()
    {
        $printNodeAPI = new PrintNodeAPI;
        $this->computers = $printNodeAPI->getComputers();

        if (!array_search($this->selectedComputer, array_column($this->computers, 'id'))) {
            return;
            // $this->selectedComputer = $this->computers[0]['id'];
            // $this->user->ComputerId = $this->selectedComputer;
            Cookie::queue('computer', $this->selectedComputer, 5 * 365 * 24 * 60);
        }
        $this->printers = $printNodeAPI->getPrinters($this->selectedComputer);


        if (!array_search($this->selectedLabelPrinter, array_column($this->printers, 'id'))) {
            // $this->selectedLabelPrinter = $this->printers[0]['id'];
            // $this->user->PrinterId = $this->selectedLabelPrinter;
            $defaultPrinter = collect($this->printers)
            ->where('state','online')
                ->filter(function ($printer) {
                    return strtolower(data_get($printer, 'name')) == 'label printer';
                })->first();

            $this->selectedLabelPrinter = data_get($defaultPrinter, 'id') ?? data_get(collect($this->printers)->where('state','online'), '0.id');

            Cookie::queue('label-printer', $this->selectedLabelPrinter, 5 * 365 * 24 * 60);
            $labelPrinter = $printNodeAPI->getPrinter($this->selectedLabelPrinter);
            session()->put('labelPrinterOnline', data_get($labelPrinter,'0.state') === 'online');
            // Cookie::queue('invoice-printer', $this->selectedInvoicePrinter, 5 * 365 * 24 * 60);
        }

        if (!array_search($this->selectedInvoicePrinter, array_column($this->printers, 'id'))) {
            $defaultPrinter = collect($this->printers)
                ->filter(function ($printer) {
                    return strtolower(data_get($printer, 'name')) == 'invoice printer';
                })->first();

            $this->selectedInvoicePrinter = data_get($defaultPrinter, 'id') ?? data_get($this->printers, '0.id');

            Cookie::queue('invoice-printer', $this->selectedInvoicePrinter, 5 * 365 * 24 * 60);
            $invoicePrinter = $printNodeAPI->getPrinter($this->selectedInvoicePrinter);
            session()->put('invoicePrinterOnline', data_get($invoicePrinter,'0.state') === 'online');
        }

        if (!array_search($this->selectedCardPrinter, array_column($this->printers, 'id'))) {
            $defaultPrinter = collect($this->printers)
            ->where('state','online')
                ->filter(function ($printer) {
                    return strtolower(data_get($printer, 'name')) == 'card printer';
                })->first();

            $this->selectedCardPrinter = data_get($defaultPrinter, 'id') ?? null;

            !$this->selectedCardPrinter ?? Cookie::queue('card-printer', $this->selectedCardPrinter, 5 * 365 * 24 * 60);
            if (!!$this->selectedCardPrinter) {
                $cardPrinter = $printNodeAPI->getPrinter($this->selectedCardPrinter);
                session()->put('cardPrinterOnline', data_get($cardPrinter,'0.state') === 'online');  
            }
        }

        if ($this->selectedComputer) {
            $this->scales = $printNodeAPI->getScales($this->selectedComputer);

            if (!array_search($this->selectedScale, array_column($this->scales, 'id'))) {
                if (is_array($this->scales) && count($this->scales)) {
                    $this->selectedScale = $this->scales[0]['productId'];
                    // $this->user->ScaleId = $this->selectedScale;
                    Cookie::queue('scale', $this->selectedScale, 5 * 365 * 24 * 60);
                }
            }
        }

        if ($this->userId != 'new') {
            $this->user->save();
        }
    }

    public function markAll()
    {
        $this->selectedShippingBoxes = ShippingBox::all()->pluck('id')->toArray();
        $this->user->shipping_boxes()->sync($this->selectedShippingBoxes);
    }

    public function mark($boxId)
    {
        if (in_array($boxId, $this->selectedShippingBoxes)) {
            $this->selectedShippingBoxes = array_filter($this->selectedShippingBoxes, function ($id) use ($boxId) {
                return $boxId != $id;
            });
        } else {
            array_push($this->selectedShippingBoxes, $boxId);
        }
        $this->user->shipping_boxes()->sync($this->selectedShippingBoxes);
    }
}
