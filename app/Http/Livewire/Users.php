<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\User;
// use Livewire\WithPagination;


class Users extends Component
{
    // use WithPagination;

    public $perPage = 25;
    public $filter = [
        // "role" => "",
        "search" => "",
        "status" => ""
    ];
    public $users;
    public $columns = ['Name', 'Email', 'Created', 'Status', 'Notify', 'Password', ''];

    public function mount()
    {
        $this->users = $this->getUsers();

        return view('livewire.users');
    }

    public function render()
    {
        return view('livewire.users');
    }

    // public function updatedusers() {
    //     $this->users->save();
    // }

    public function updatedfilter($value)
    {
        $this->users = $this->getUsers();
    }

    public function resetFilter()
    {
        foreach ($this->filter as $key => $value) {
            $this->filter[$key] = '';
        }

        $this->users = $this->getUsers();
    }

    private function getUsers()
    {
        $search = $this->filter['search'];

        $users = User::oldest();

        // if(!empty($this->filter['role'])){
        //     $shipments->where('user_id', $this->filter['user']);
        // }

        if (!empty($this->filter['status'])) {
            $users->where('status', $this->filter['status']);
        }

        if (!empty($search)) {
            $users->where('name', 'like', "%" . $search . "%")
                ->orWhere('email', 'like', "%" . $search . "%")
                ->orWhere('SellerCloudUsername', 'like', "%" . $search . "%");
            //   ->orWhereHas('user', function($q) use($search) {
            //         $q->where('name', 'like', '%' . $search . '%');
            //     });

        }

        $users = $users->get();

        foreach ($users as &$user) {
            $words = explode(' ', $user->name);
            $user->initials = strtoupper(substr($words[0], 0, 1) . substr(end($words), 0, 1));
        }

        return $users;
    }
}
