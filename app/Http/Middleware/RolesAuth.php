<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Log;
use DB;

class RolesAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // $role = DB::table('users')->leftJoin('roles', 'users.role_id', 'roles.id')->where('users.id', auth()->user()->id)->first();
        // info('bye123');
        // info(\App\Models\Shipment::$has_access);
        // info(auth()->user()->email);
        if (!isAdmin()) {
            return redirect('/');
        }

        return $next($request);
    }
}
