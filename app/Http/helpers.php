<?php

use Illuminate\Support\Facades\Mail;
use App\Mail\AdminEmail;

function sendAdminMail($emailMessage)
{
    Log::channel('it-slack')->info($emailMessage);
    return;

    Mail::to("<EMAIL>")
        ->cc("<EMAIL>")
        ->send(new AdminEmail($emailMessage));
}

function logOrder($orderId, $message, $level = 'info')
{
    $orderId = $orderId ?? 'failed_' . today()->toDateString();

    config(['logging.channels.order.path' => storage_path("logs/orders/$orderId.log")]);

    Log::channel('order')->info(data_get(debug_backtrace(), '1.file') . ':' . data_get(debug_backtrace(), '1.line'));

    Log::channel('order')->$level($message);
}

function isAdmin()
{
    return auth()->user()->role_id == 1 || auth()->user()->role_id == 3;

    return collect(\App\Models\Shipment::$has_access)
        ->contains(auth()->user()->email);
}
