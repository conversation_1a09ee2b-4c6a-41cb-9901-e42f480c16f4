<?php

namespace App\Jobs;

use App\Http\Livewire\Ship;
use App\Library\Services\SellerCloudAPI;
use App\Models\FailedApiRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Shipment;

class SendOrderToSellerCloud implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $order;

    protected $label;

    protected $sortboxId;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 4;

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array
     */
    public function backoff()
    {
        return [60, 5 * 60, 60 * 60];
    }

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order, $label, $sortboxId)
    {
        $this->order = $order;

        $this->label = $label;

        $this->sortboxId = $sortboxId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $status = optional(FailedApiRequest::where('order_id', $this->order['OrderID'])->first())->status;
        if ($status && $status != 'Unresolved') {
            logOrder($this->order['OrderID'], 'SendOrderToSellerCloud line 65');
            return;
        }
        $sellerCloudAPI = new SellerCloudAPI;
        $response = $sellerCloudAPI->shipPackage($this->order, $this->label);
        // because shippackage id in a try catch we have to check if we have a response
        // if (!$response) {
        //     FailedApiRequest::firstOrCreate(
        //         ['order_id' => $this->order['OrderID']],
        //         [
        //             'type' => 'SellerCloud',
        //             'order' => $this->order,
        //             'label' => $this->label,
        //             'sortbox_id' => $this->sortboxId,
        //             'error' =>  'Not marked as shipped on Sellercloud',
        //             'status' => 'Unresolved',
        //         ]
        //     );
        //     // throw new \Exception();
        // }

        $order = $sellerCloudAPI->getOrder($this->order['OrderID']);
        if (data_get($order, 'Statuses.ShippingStatus') == 3) {

            $sellerCloudAPI->addCustomerServiceNote($this->order['OrderID'], 'Shipped by: ' . optional(Shipment::where('order_id', $this->order['OrderID'])->where('voided', 0)->first())->user->email);

            (new Ship)->emptySortbox($this->sortboxId, $this->order, $this->label);

            $failedApiRequest =  FailedApiRequest::where('order_id', data_get($this->order, 'OrderID'))
                ->where('type', 'SellerCloud')
                ->where('status', 'Unresolved')
                ->first();

            optional($failedApiRequest)->update([
                'status' => 'Resolved',
                'resolved_by' => 'Auto',
            ]);
        } else {
            if ($this->attempts() >= 3) {
                FailedApiRequest::firstOrCreate(
                    ['order_id' => $this->order['OrderID']],
                    [
                        'type' => 'SellerCloud',
                        'order' => $this->order,
                        'label' => $this->label,
                        // 'printer_id' => request()->cookie('label-printer'),
                        'sortbox_id' => $this->sortboxId,
                        'error' =>  'Not marked as shipped on Sellercloud',
                        'status' => 'Unresolved',
                    ]
                );
            }

            logOrder($this->order['OrderID'], 'Not marked as shipped on Sellercloud');
            throw new \Exception();
        }
    }
}
