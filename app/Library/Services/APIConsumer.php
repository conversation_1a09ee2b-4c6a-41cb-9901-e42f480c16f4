<?php

namespace App\Library\Services;

use Illuminate\Support\Facades\Http;
use Log;

class APIConsumer
{
    protected string $authType;

    protected function makeAPIRequest(string $type, string $endpoint, $params = NULL, string $url = NULL, $log = true)
    {
        $headers = [
            'accept' => '*/*',
            'Accept-Encoding' => 'gzip, deflate, br'
        ];

        if ($this->authType == 'token' && $this->token != '') {
            $headers['Authorization'] = "Bearer " . $this->token;
        } elseif ($this->authType == 'apiKey') {
            $headers['API-Key'] = $this->apiKey;
        } elseif ($this->authType == 'basic') {
            $headers['Authorization'] = "Basic " . base64_encode($this->username . ':' . $this->password);
        }

        try {
            $user = auth()->user();
            $url = ($url ? $url : $this->baseUrl) . $endpoint;

            if ($type == 'GET') {
                $this->response = Http::withHeaders($headers)
                    ->get($url, $params);

                $this->responseBody = $this->response->body();
                $this->status = $this->response->status();
            } elseif ($type == 'PUT') {
                // $requestBody = json_encode($params);

                $this->response = Http::withHeaders($headers)
                    ->put($url, $params);

                $this->responseBody = $this->response->body();
                $this->status = $this->response->status();
            } elseif ($type == 'PATCH') {
                // $requestBody = json_encode($params);

                $this->response = Http::withHeaders($headers)
                    ->patch($url, $params);

                $this->responseBody = $this->response->body();
                $this->status = $this->response->status();
            } else {
                $this->response = Http::withHeaders($headers)
                    ->post($url, $params);
                // dump($this->response);
                $this->responseBody = $this->response->body();
                $this->status = $this->response->status();
            }

            $jsonParams = ($params ? json_encode($params) : "[]");


            if (!app()->runningInConsole()) {
                $userString = $user->email;
            } else {
                $userString = "CLI";
            }
            if ($log) {
                Log::debug("User: $userString\n URL: $url\n Params: $jsonParams\n Status: $this->status\n Response: $this->responseBody");
            }

            $responseObject = json_decode($this->responseBody, TRUE) ?? $this->responseBody;
            return $responseObject;
        } catch (\Exception $e) {
            report($e);
            Log::error($e->getMessage());
        }
    }
}
