<?php

namespace App\Library\Services;

use App\Library\Services\APIConsumer;
use Log;
use App\Models\Setting;

class FabbleAPI extends APIConsumer
{
    // todo base url
    // protected string $baseUrl = 'http://api.photo.test:8001/api/';
    protected string $baseUrl = 'https://fabble-dev.capitalc.co/api/';

    protected string $apiKey;
    protected string $authType = '';

    function __construct()
    {
    }

    public function checkIfCanceled($sourceOrderId, $orderId)
    {
        $endpoint = 'order-status-and-invoice';

        $params = [
            'ss_key' => config('services.fabble_key'),
            'order_id' => $sourceOrderId
        ];
        try {
            $response = $this->makeAPIRequest('POST', $endpoint, $params);

            logOrder($orderId, $response);

            return $response;
        } catch (\Exception $e) {
            report($e);
            logOrder($orderId, $e);
        }
    }



    public function shipOrder($label, $sourceOrderId, $orderId, $estimatedDeliveryDate)
    {
        $endpoint = 'label-was-created';

        $params = [
            'ss_key' => config('services.fabble_key'),
            'order_id' => $sourceOrderId,
            'tracking_number' => $label['tracking_number'],
            'carrier_code' => $label['carrier_code'],
            'arrival_date' => $estimatedDeliveryDate,
            'shipment_cost' => $label['shipment_cost']['amount'],
            'carrier_id' => $label['carrier_id'],
            'service_code' => $label['service_code'],

        ];
        try {
            $response = $this->makeAPIRequest('POST', $endpoint, $params);

            logOrder($orderId, $response);
        } catch (\Exception $e) {

            report($e);

            logOrder($orderId, $e);
        }
    }






    public function unship($sourceOrderId, $orderId)
    {
        $endpoint = 'label-was-voided';

        $params = [
            'ss_key' => config('services.fabble_key'),
            'order_id' => $sourceOrderId
        ];
        try {
            $response = $this->makeAPIRequest('POST', $endpoint, $params);

            logOrder($orderId, $response);
        } catch (\Exception $e) {
            report($e);

            logOrder($orderId, $e);
        }
    }
}
