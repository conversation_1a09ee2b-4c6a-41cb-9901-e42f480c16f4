<?php

namespace App\Library\Services;

use App\Library\Services\APIConsumer;
use Log;

class PrintNodeAPI extends APIConsumer
{
    protected string $baseUrl = 'https://api.printnode.com/';
    protected string $username1 = "WW_gGpUxVlYfldbAmrR7J3eD5HqURGfJCHzkbGZPzQ0";
    protected string $username = "NMJWBTjRyTY3t1Hbt4ngjx_dgh45jz4C12szOCBkCBg";
    protected string $password = "";
    protected string $authType = 'basic';

    function __construct($key = "")
    {
        try {
            $user = auth()->user();
            if ($user && !$key) {
                $key = $user->PrintNodeApiKey;
            }

            if ($key) {
                $this->username = $key;
            } else {
                // throw new \Exception("Please select a PrintNode account on the Users page", 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::error($e->getMessage());
        }
    }

    public function whoami()
    {
        return $this->makeAPIRequest('GET', 'whoami');
    }

    public function getComputers()
    {
        return $this->makeAPIRequest('GET', 'computers');
    }

    public function getComputer($computerId)
    {
        if ($computerId) {
            return $this->makeAPIRequest('GET', "computers/$computerId", null, null, false);
        }
        return [];
    }

    public function getPrinters($computer = NULL)
    {
        $url = ($computer ? 'computers/' . $computer . '/printers' : 'printers');
        return $this->makeAPIRequest('GET', $url);
    }

    public function getPrinter($printerId)
    {
        if ($printerId) {
            return $this->makeAPIRequest('GET', "printers/$printerId", null, null, false);
        }
        return [];
    }

    public function getScales($computer)
    {
        return $this->makeAPIRequest('GET', 'computer/' . $computer . '/scales', null, Null, false);
    }

    public function print(string $url, $title = 'P4L label', $orderId = null, $printerId = Null, $contentType='pdf_uri')
    {
        try {
            $user = auth()->user() ? auth()->user()->email : 'CLI';

            $request = [
                // 'printerId' => $user->PrinterId,
                'printerId' =>  $printerId ?? request()->cookie('label-printer'),
                'title' => $title . ", User:" . $user,
                'contentType' => $contentType,
                'content' => $url,
                'source' => 'Ship P4L'
            ];

            $response = $this->makeAPIRequest('POST', 'printjobs', $request);

            logOrder($orderId, $response);

            if ($this->status == 201) {
                $printJob = $this->getPrintJobSentOrError($response);

                logOrder($orderId, $printJob);

                if ($printJob['state'] != 'done') {
                    throw new \Exception(data_get($printJob, 'code') . ': ' . data_get($printJob, 'message'), 1);
                }
            } else {
                throw new \Exception(data_get($response, 'code') . ': ' . data_get($response, 'message'), 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            logOrder($orderId, $e->getMessage());
            Log::error($e->getMessage());
            return FALSE;
        }

        return $printJob;
    }

    public function getApiKey()
    {
        return $this->username;
    }

    private function getPrintJobSentOrError($printJobId)
    {
        $attempts = 0;

        do {
            $response = $this->makeAPIRequest('GET', 'printjobs/' . $printJobId)[0];
            $attempts++;

            // Wait 2 seconds
            usleep(2000000);
        } while ($response['state'] != 'done' && $response['state'] != 'error' && $attempts < 10);

        return $response;
    }
}
