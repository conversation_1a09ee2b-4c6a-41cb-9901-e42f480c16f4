<?php

namespace App\Library\Services;

use App\Library\Services\APIConsumer;
use App\Models\Shipment;
use Illuminate\Support\Facades\Http;
use Log;

class SellerCloudAPI extends APIConsumer
{
    protected string $baseUrl = 'https://pfl.api.sellercloud.com/rest/api/';
    // protected string $username = "<EMAIL>";
    // protected string $password = "pkVz4*Tmy3yN59b^";

    // protected string $username = "<EMAIL>";
    // protected string $password = "Sz520015";
    protected string $token = '';
    protected string $authType = 'token';
    protected $lightwall = FALSE;
    protected $soap = FALSE;

    private function getToken()
    {
        $this->token = "";

        $endpoint = "token";
        $params = [
            'Username' => env('SC_USERNAME'),
            'Password' => env('SC_PASSWORD'),
        ];

        $url = $this->lightwall ? 'https://pfl.api.sellercloud.com/' : NULL;

        $response = $this->makeAPIRequest('POST', $endpoint, $params, $url);

        if (is_array($response) && array_key_exists('access_token', $response)) {

            cache()->remember(
                $this->getTokenKey(),
                now()->addMinutes(29),
                fn () => $response['access_token']
            );

            $this->token = $response['access_token'];
            // session()->put('sc_token', $response['access_token']);
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function makeSellerCloudRequest(string $type, string $endpoint, $params = NULL)
    {
        // if (session()->has('sc_token')) {
        //     $this->token = session()->get('sc_token');
        // }
        $this->token = (cache()->get($this->getTokenKey(), '') ?? '');

        $response = $this->makeAPIRequest($type, $endpoint, $params, NULL);

        if ($this->status == 401) {
            if ($this->getToken()) {
                $response = $this->makeAPIRequest($type, $endpoint, $params);
            } else {
                throw new \Exception("Problem obtaing new token from SellerCloud", 1);
            }
        }

        return $response;
    }

    public function getOrders()
    {
        $endpoint = "Orders/GetAllByView";
        $params = [
            'pageNumber' => 1,
            'pageSize' => 10,
            'viewID' => 13 // View ID 13 is ready for ship orders
        ];

        $response = $this->makeSellerCloudRequest('GET', $endpoint, $params);

        return $response;
    }

    public function getOrder(string $orderId)
    {
        $endpoint = "Orders/" . $orderId;

        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        logOrder($orderId, $response);

        return $response;
    }

    public function getOrdersBySku($sku, $picklist)
    {
        $orderIds = (count($picklist['singleItemOrderIds']) ? $picklist['singleItemOrderIds'] : $picklist['multiItemOrderIds']);

        $endpoint = "Orders";
        $params = [
            'sKU' => $sku,
            // 'orderStatus' => ['Confirmed'],
            // 'shippingStatus' => 'Unshipped',
            // 'orderIDs' => $orderIds
        ];

        $response = $this->makeSellerCloudRequest('GET', $endpoint, $params);

        return $response;
    }

    public function shipPackage(array $order, array $label)
    {
        try {
            // TODO: Have a field in the database for masked carrier names
            if ($label['carrier_code'] == 'endicia') {
                $label['carrier_code'] = 'usps';
            } elseif ($label['carrier_code'] == 'dhl_global_mail') {
                if ($order['OrderDetails']['OrderSource'] == 64) { // Reverb orders still need to say dhl_global_mail
                    $label['carrier_code'] = 'DHLGlobalMail';
                } else {
                    $label['carrier_code'] = 'dhl';
                }

                $label['service_code'] = 'DHL global';
            } elseif ($label['carrier_code'] == 'amazon_buy_shipping') {
                preg_match('/amazon_([a-zA-Z]+)_/',  $label['service_code'], $array);
                $label['carrier_code'] = data_get($array, '1');
            }

            $endpoint = "Orders/ShippingStatus";
            $params = [
                'OrderID' => $order['OrderID'],
                'ShippingCarrier' => strtoupper($label['carrier_code']),
                'shippingService' => $label['service_code'] . (data_get($order, 'saturday_delivery') == true ? '_saturday' : ''),
                'ShipDate' => now()->subMinute(10)->format("Y-m-d H:i:s"),
                // 'ShipDate' => Shipment::where('order_id', $order['OrderID'])->where('voided', 0)->first()->created_at,
                'TrackingNumber' => $label['tracking_number'],
                'ShippingCost' => $label['shipment_cost']['amount'],
                'Width' => $label['packages'][0]['dimensions']['width'],
                'Height' => $label['packages'][0]['dimensions']['height'],
                'Length' => $label['packages'][0]['dimensions']['length'],
                'Weight' => $label['packages'][0]['weight']['value']
            ];

            $response = $this->makeSellerCloudRequest('PUT', $endpoint, $params);

            logOrder($order['OrderID'], $response);
            // Log::debug("Sending shipping infor to SC. Order: " . json_encode($order));
            // Log::debug("Sending shipping infor to SC. Label: " . json_encode($label));
            // Log::debug("Sending shipping infor to SC. Status: " . $this->status . " Response: " . json_encode($response));

            if ($this->status != 200) {
                throw new \Exception($response, 1);
            }
            return $response;
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            logOrder($order['OrderID'], $e->getMessage());
            Log::error($e->getMessage());
        }
    }

    public function addCustomerServiceNote($orderId, $note)
    {
        try {
            $endpoint = "Orders/${orderId}/Notes";

            $params = [
                'Message' => $note,
                'Category' => 'General'
            ];

            $response = $this->makeSellerCloudRequest('POST', $endpoint, $params);

            logOrder($orderId, $response);

            if ($this->status != 200) {
                throw new \Exception($response, 1);
            }

            return $response;
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::error($e->getMessage());
        }
    }

    public function unshipPackage(string $orderId)
    {
        $endpoint = "Orders/SetUnshipped";
        $params = [
            'Orders' => [$orderId],
            'PIN' => ''
        ];

        $response = $this->makeSellerCloudRequest('PUT', $endpoint, $params);

        logOrder($orderId, $response);

        return $response;
    }

    public function getNotes($orderId)
    {
        $endpoint = "/Orders/Notes?id=$orderId&Category=2";

        $response = collect($this->makeSellerCloudRequest('GET', $endpoint))->map(function ($note) {
            $note['done'] = false;
            return $note;
        });

        logOrder($orderId, $response);

        $response = collect($response)->filter(function ($note) {
            return $note['Category'] == 2;
        })->values();

        return $response;
    }

    public function getProductDetails($productId, $orderId)
    {

        $endpoint = "Catalog?model.sku=" . $productId;

        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        logOrder($orderId, $response);

        if ($this->status != 200) {
            throw new \Exception($response['message'], 1);
        }
        return $response;
    }

    public function getInvoice($orderId)
    {
        $endpoint = 'Orders/PrintPdfInvoice';

        $params = [
            'OrderIDs' => [$orderId],
        ];

        $response = $this->makeSellerCloudRequest('POST', $endpoint, $params);

        logOrder($orderId, $response);

        return $response;
    }

    public function getTokenKey()
    {
        return ($this->lightwall ? 'lightwall_' : '') . 'sc_token';
    }
}

// class SellerCloudLightwallAPI extends SellerCloudAPI
// {
//     protected string $baseUrl = 'https://pfl.api.sellercloud.com/lightwall/';
//     protected $lightwall = TRUE;

//     public function getPicklist($picklistId)
//     {
//         $endpoint = "picklists/open/" . $picklistId;

//         $response = $this->makeSellerCloudRequest('GET', $endpoint);

//         if ($this->status != 200) {
//             if (array_key_exists('message', $response)) {
//                 throw new \Exception($response['message'], 1);
//             } elseif (array_key_exists('errors', $response)) {
//                 throw new \Exception($response['errors']['id'][0], 1);
//             }
//         }

//         return $response;
//     }

//     public function getSortbox($hardwareId)
//     {
//         $hardwareId = substr($hardwareId, 2);
//         // $hardwareId = str_replace('-', '', $hardwareId);
//         $endpoint = "sort-boxes/by-crc/" . $hardwareId;

//         $response = $this->makeSellerCloudRequest('GET', $endpoint);

//         if ($this->status != 200) {
//             throw new \Exception($response['message'], 1);
//         }

//         return $response;
//     }

//     public function getSortboxById($id)
//     {
//         $endpoint = "sort-boxes/" . $id;

//         $response = $this->makeSellerCloudRequest('GET', $endpoint);

//         if ($this->status != 200) {
//             throw new \Exception($response['message'], 1);
//         }

//         return $response;
//     }

//     public function getOrderBySortbox($sortboxId)
//     {
//         $endpoint = "sort-boxes/" . $sortboxId . "/order";
//         $response = $this->makeSellerCloudRequest('GET', $endpoint);

//         if ($this->status != 200) {
//             throw new \Exception($response['message'], 1);
//         }

//         return $response;
//     }

//     public function emptyBox($sortboxId)
//     {
//         $endpoint = "sort-boxes/" . $sortboxId . "/empty";
//         $response = $this->makeSellerCloudRequest('PATCH', $endpoint);

//         if ($this->status != 200) {
//             throw new \Exception($response['message'], 1);
//         }

//         return $response;
//     }
// }

// class SellerCloudSoapAPI extends SellerCloudAPI
// {
//     protected string $baseUrl = 'https://pfl.ws.sellercloud.com/scservice.asmx';
//     protected $soap = TRUE;

//     public function getOrderIdBySku($sku, $picklistId)
//     {
//         $endpoint = "";
//         $body = <<<EOT
//             <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
//             <Header>
//                 <AuthHeader xmlns="http://api.sellercloud.com/">
//                     <UserName>$this->username</UserName>
//                     <Password>$this->password</Password>
//                 </AuthHeader>
//                 <ServiceOptions xmlns="http://api.sellercloud.com/">
//                 </ServiceOptions>
//             </Header>
//             <Body>
//                 <Orders_GetOrdersReadyToShipOptimized xmlns="http://api.sellercloud.com/">
//                     <Filters>
//                         <Keys>
//                             <string>SKU</string>
//                             <string>Picklist</string>
//                         </Keys>
//                         <Values>
//                             <string>$sku</string>
//                             <string>$picklistId</string>
//                         </Values>
//                     </Filters>
//                 </Orders_GetOrdersReadyToShipOptimized>
//             </Body>
//         </Envelope>
// EOT;

//         $response = $this->makeSellerCloudRequest('POST', $endpoint, $body);

//         if ($this->status != 200) {
//             if (array_key_exists('message', $response)) {
//                 throw new \Exception($response['message'], 1);
//             } elseif (array_key_exists('errors', $response)) {
//                 throw new \Exception($response['errors']['id'][0], 1);
//             }
//         } else if (!$response) {
//             throw new \Exception("No matching items found", 1);
//         }

//         return $response;
//     }

//     protected function makeAPIRequest(string $type, string $endpoint, $params = NULL, string $url = NULL)
//     {
//         $headers = [
//             'accept' => '*/*',
//             'Accept-Encoding' => 'gzip, deflate, br'
//         ];

//         try {
//             $user = auth()->user();
//             $url = $this->baseUrl . $endpoint;

//             $this->response = Http::contentType("text/xml")->send($type, $url, ['body' => $params]);

//             $this->responseBody = $this->response->body();
//             $this->status = $this->response->status();


//             Log::debug("User: $user->email\n URL: $url\n Body: $params\n Status: $this->status\n Response: $this->responseBody");

//             // TODO get from XML data
//             $responseObject = $this->getXmlResponse($this->responseBody);
//             return $responseObject;
//         } catch (\Exception $e) {
//             Log::error($e->getMessage());
//             session()->flash('message', $e->getMessage());
//         }
//     }

//     function getXmlResponse($string)
//     {
//         $xml = simplexml_load_string(trim($string), "SimpleXMLElement", LIBXML_NOCDATA);

//         $orderId = $xml->children('http://schemas.xmlsoap.org/soap/envelope/')->Body->children()->Orders_GetOrdersReadyToShipOptimizedResponse->Orders_GetOrdersReadyToShipOptimizedResult->int;

//         if (!$orderId) {
//             throw new \Exception("Error with Soap request", 1);
//         }

//         return (string)$orderId;
//     }
// }
