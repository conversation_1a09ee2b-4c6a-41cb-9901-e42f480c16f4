<?php

namespace App\Library\Services;

use App\Library\Services\APIConsumer;
use Illuminate\Support\Facades\Http;
use Log;



class SellerCloudLightwallAPI extends SellerCloudAPI
{
    protected string $baseUrl = 'https://pfl.api.sellercloud.com/lightwall/';
    protected $lightwall = TRUE;

    public function getPicklist($picklistId)
    {
        $endpoint = "picklists/open/" . $picklistId;

        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        if ($this->status != 200) {
            if (array_key_exists('message', $response)) {
                throw new \Exception($response['message'], 1);
            } elseif (array_key_exists('errors', $response)) {
                throw new \Exception($response['errors']['id'][0], 1);
            }
        }

        return $response;
    }

    public function getSortbox($hardwareId)
    {
        $hardwareId = substr($hardwareId, 2);
        // $hardwareId = str_replace('-', '', $hardwareId);
        $endpoint = "sort-boxes/by-crc/" . $hardwareId;

        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        if ($this->status != 200) {
            throw new \Exception(data_get($response, 'message'), 1);
        }

        return $response;
    }

    public function getOrdersByBarcode($picklistId, $barcode)
    {
        $endpoint = "picklists/$picklistId/orders/by-barcode/$barcode?pickingStatus=FullyPicked&shippingStatus=Unshipped&priority=SingleUnit";

        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        if ($this->status != 200) {
            throw new \Exception($response['message'], 1);
        }

        return $response;
    }

    public function getSortboxById($id, $orderId)
    {
        $endpoint = "sort-boxes/" . $id;

        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        logOrder($orderId, $response);

        if ($this->status != 200) {
            throw new \Exception($response['message'], 1);
        }

        return $response;
    }

    public function getOrderBySortbox($sortboxId)
    {
        $endpoint = "sort-boxes/" . $sortboxId . "/order";
        $response = $this->makeSellerCloudRequest('GET', $endpoint);

        logOrder(data_get($response, 'id'), $response);

        if ($this->status != 200) {
            throw new \Exception(data_get($response, 'message'), 1);
        }

        return $response;
    }

    public function emptyBox($sortboxId, $orderId)
    {
        $endpoint = "sort-boxes/" . $sortboxId . "/empty";
        $response = $this->makeSellerCloudRequest('PATCH', $endpoint);

        logOrder($orderId, $response);

        if ($this->status != 200) {
            throw new \Exception($response['message'], 1);
        }

        return $response;
    }

    public function flashLight($sortboxId)
    {
        $endpoint = "remote-control/sort-boxes/$sortboxId/flash";

        $response = $this->makeSellerCloudRequest('POST', $endpoint, []);

        // return $response;
    }
}
