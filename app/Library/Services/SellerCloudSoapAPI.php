<?php

namespace App\Library\Services;

use App\Library\Services\APIConsumer;
use Illuminate\Support\Facades\Http;
use Log;

class SellerCloudSoapAPI extends SellerCloudAPI
{
    protected string $baseUrl = 'https://pfl.ws.sellercloud.com/scservice.asmx';
    protected $soap = TRUE;

    public function getOrderIdBySku($sku, $picklistId)
    {
        $endpoint = "";
        $body = <<<EOT
            <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
            <Header>
                <AuthHeader xmlns="http://api.sellercloud.com/">
                    <UserName>$this->username</UserName>
                    <Password>$this->password</Password>
                </AuthHeader>
                <ServiceOptions xmlns="http://api.sellercloud.com/">
                </ServiceOptions>
            </Header>
            <Body>
                <Orders_GetOrdersReadyToShipOptimized xmlns="http://api.sellercloud.com/">
                    <Filters>
                        <Keys>
                            <string>SKU</string>
                            <string>Picklist</string>
                        </Keys>
                        <Values>
                            <string>$sku</string>
                            <string>$picklistId</string>
                        </Values>
                    </Filters>
                </Orders_GetOrdersReadyToShipOptimized>
            </Body>
        </Envelope>
EOT;

        $response = $this->makeSellerCloudRequest('POST', $endpoint, $body);

        if ($this->status != 200) {
            if (array_key_exists('message', $response)) {
                throw new \Exception($response['message'], 1);
            } elseif (array_key_exists('errors', $response)) {
                throw new \Exception($response['errors']['id'][0], 1);
            }
        } else if (!$response) {
            throw new \Exception("No matching items found", 1);
        }

        return $response;
    }

    protected function makeAPIRequest(string $type, string $endpoint, $params = NULL, string $url = NULL)
    {
        $headers = [
            'accept' => '*/*',
            'Accept-Encoding' => 'gzip, deflate, br'
        ];

        try {
            $user = auth()->user();
            $url = $this->baseUrl . $endpoint;

            $this->response = Http::contentType("text/xml")->send($type, $url, ['body' => $params]);

            $this->responseBody = $this->response->body();
            $this->status = $this->response->status();


            Log::debug("User: $user->email\n URL: $url\n Body: $params\n Status: $this->status\n Response: $this->responseBody");

            // TODO get from XML data
            $responseObject = $this->getXmlResponse($this->responseBody);
            return $responseObject;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            session()->flash('message', $e->getMessage());
        }
    }

    function getXmlResponse($string)
    {
        $xml = simplexml_load_string(trim($string), "SimpleXMLElement", LIBXML_NOCDATA);

        $orderId = $xml->children('http://schemas.xmlsoap.org/soap/envelope/')->Body->children()->Orders_GetOrdersReadyToShipOptimizedResponse->Orders_GetOrdersReadyToShipOptimizedResult->int;

        if (!$orderId) {
            throw new \Exception("Error with Soap request", 1);
        }

        return (string)$orderId;
    }
}
