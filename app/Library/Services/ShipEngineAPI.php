<?php

namespace App\Library\Services;

use App\Library\Services\APIConsumer;
use Log;
use App\Models\Setting;

class ShipEngineAPI extends APIConsumer
{
    protected string $baseUrl = 'https://api.shipengine.com/v1/';
    // protected string $apiKey = 'NG7LOy6+eZ/K9dfB1HWPZZtJDyrJNCO2PmUxomKWYe4';
    // protected string $apiKey = 'TEST_kOA6HG3XXPFzsx19sBN0H14zfwey3wf2sgAU12Wbidk';
    protected string $apiKey;
    protected string $authType = 'apiKey';
    private int $unshipTries = 1;

    function __construct()
    {
        // $this->apiKey = "TEST_hbr0mxVxmUSVx0RvvibGOLwEsVz7Tz5+tw4gPYEB+fA";
        $this->apiKey = Setting::first()->seAPIKey;
    }

    public function getCarriers()
    {
        $response = $this->makeAPIRequest('GET', 'carriers');
        return $response['carriers'];
    }

    public function getPackageTypes($carriers)
    {
        $packageTypes = [];

        foreach ($carriers as $carrier) {
            $response = $this->makeAPIRequest('GET', 'carriers/' . $carrier . '/packages');
            $packageTypes = array_merge($packageTypes, $response['packages']);
        }
        return $packageTypes;
    }

    public function getRates($request, $orderId)
    {
        $response = $this->makeAPIRequest('POST', 'rates', $request);

        logOrder($orderId, $response);

        return $response;
    }

    public function createLabel($rateId)
    {
        return $this->makeAPIRequest('POST', 'labels/rates/' . $rateId, ['we need to send something' => 'so here goes']);
    }

    public function createLabelFromScratch($request, $orderId)
    {
        $response = $this->makeAPIRequest('POST', 'labels', $request);

        logOrder($orderId, $response);

        return $response;
    }

    public function validateAddress($address, $orderId)
    {
        $response = $this->makeAPIRequest('POST', 'addresses/validate', $address);

        logOrder($orderId, $response);

        return $response;
    }

    public function getLabel($labelId)
    {
        try {
            $response = $this->makeAPIRequest('GET', 'labels/' . $labelId);

            if (array_key_exists('errors', $response)) {
                throw new Exception($response['errors'][0]['error_code'], 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::error($e->getMessage());
        }

        return $response;
    }

    // public function getLabels() {
    //     try {
    //         $response = $this->makeAPIRequest('GET', 'labels?page=1&page_size=200&sort_by=created_at&sort_dir=desc&created_at_start=' . date('Y-m-d', strtotime("-2 days")));

    //         if(!array_key_exists('labels', $response) && array_key_exists('errors', $response)) {
    //             throw new Exception($response['errors'][0]['message'], 1);
    //         }
    //     } catch (\Exception $e) {
    //         session()->flash('message', $e->getMessage());
    //         Log::error($e->getMessage());
    //     }

    //     usort($response['labels'], function($a, $b) {
    //         return strtotime($b['created_at']) - strtotime($a['created_at']);
    //     });

    //     return $response['labels'];
    // }

    public function getLabels($voided = FALSE)
    {
        $page = 1;
        $labels = [];

        try {
            do {
                $response = $this->makeAPIRequest('GET', "labels?page=$page&page_size=200" . ($voided ? "&label_status=voided" : ""));

                if (!array_key_exists('labels', $response) && array_key_exists('errors', $response)) {
                    throw new \Exception($response['errors'][0]['message'], 1);
                }

                $labels = array_merge($response['labels'], $labels);
                $page++;
            } while ($page < $response['pages']);
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::error($e->getMessage());
        }

        usort($labels, function ($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $labels;
    }

    public function getShipments()
    {
        try {
            $response = $this->makeAPIRequest('GET', 'shipments?page_size=200&sort_by=created_at&sort_dir=asc&created_at_start=' . date('Y-m-d', strtotime("-2 days")));

            if (!array_key_exists('shipments', $response) && array_key_exists('errors', $response)) {
                throw new Exception($response['errors'][0]['message'], 1);
            }
        } catch (\Exception $e) {
            report($e);
            session()->flash('message', $e->getMessage());
            Log::error($e->getMessage());
        }

        usort($response['shipments'], function ($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $response['shipments'];
    }

    public function unship($labelId, $orderId)
    {
        $response = $this->makeAPIRequest('PUT', 'labels/' . $labelId . '/void', ['we need to send something' => 'so here goes']);

        logOrder($orderId, $response);

        if (array_key_exists('approved', $response) && $response['approved']) {
            session()->flash('message', $response['message']);
        } else {
            if ($this->unshipTries < 3) {
                $this->unshipTries++;
                $response = $this->unship($labelId, $orderId);

                logOrder($orderId, $response);
            } else {
                throw new \Exception('Denied ' . $this->unshipTries . ' times: ' . isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : '', 1);
            }
        }

        return $response;
    }

    public function createManifest($carrierId, $date, $warehouseId)
    {
        $request = [
            'carrier_id' => $carrierId,
            'warehouse_id' => $warehouseId, // TODO, maybe this should become dynamic
            'ship_date' => date("Y-m-d", strtotime($date))
        ];

        return $this->makeAPIRequest('POST', 'manifests', $request);
    }

    public function getManifests()
    {
        return $this->makeAPIRequest('GET', 'manifests');
    }

    public function getWareHouses() {
        return $this->makeAPIRequest('GET', 'warehouses');
    }

    public function updateWareHouse($warehouseId) {
        
        $data = [
            "warehouse_id" => "se-580287",
            "is_default" => true,
            "name" => "Fabble",
            "created_at" => "2020-11-10T00:18:30.09Z",
            "origin_address" => [
              "name" => "Fabble",
              "phone" => "************",
              "email" => null,
              "company_name" => "Fabble",
              "address_line1" => "449 Blair Rd",
              "address_line2" => "",
              "address_line3" => null,
              "city_locality" => "Avenel",
              "state_province" => "NJ",
              "postal_code" => "07001",
              "country_code" => "US",
              "address_residential_indicator" => "no",
            ],
            "return_address" => [
              "name" => "Fabble",
              "phone" => "************",
              "email" => null,
              "company_name" => "Fabble",
              "address_line1" => "449 Blair Rd",
              "address_line2" => "",
              "address_line3" => null,
              "city_locality" => "Avenel",
              "state_province" => "NJ",
              "postal_code" => "07001",
              "country_code" => "US",
              "address_residential_indicator" => "no",
            ],
          ];
        return $this->makeAPIRequest('PUT', 'warehouses/' . $warehouseId, $data);
    }

}
