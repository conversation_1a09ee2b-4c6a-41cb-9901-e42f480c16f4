<?php

namespace App\Models;

use App\Emails\DailyErrorEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FailedApiRequest extends Model
{
    use HasFactory;

    protected $guarded = [];

    public $casts = [
        'order' => 'array',
        'label' => 'array',
    ];

    public function shipment()
    {
        return $this->belongsTo(Shipment::class, 'order_id', 'order_id');
    }

    public  static function SendActiveIssuesEmail()
    {
        $failedApiRequests = FailedApiRequest::where('status', 'Unresolved')->get(['order_id', 'error']);

        if (!$failedApiRequests->count()) {
            return;
        }

        $users = User::where('receive_notifications', true)
            ->where('status', 'active')
            ->get(['name', 'email']);

        $issues = $failedApiRequests
            ->map(function ($failedApiRequest) {
                return [
                    'order_id' => $failedApiRequest->order_id,
                    'issue' => $failedApiRequest->error,
                    'shipper' => $failedApiRequest->shipment->user->name,
                    'link' => config('app.url') . "/issues?search=$failedApiRequest->order_id",
                ];
            });

        $users->each(function ($user) use ($issues) {
            dispatch(function () use ($user, $issues) {
                (new DailyErrorEmail($issues))->sendTo($user);
            });
        });

        // $users->each(function ($user) use ($issues) {
        //     dispatch(function () use ($user, $issues) {
        //         (new DailyErrorEmail($issues))
        //             // ->withData($issues)
        //             ->sendTo($user);
        //     });
        // });
    }
}
