<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScService extends Model
{
    use HasFactory;

    protected $table = 'sc_shipping_services';
    protected $fillable = ['service_id', 'sellercloud_name'];

    public function seService()
    {
        return $this->belongsTo(SeService::class, 'service_id', 'id');
    }
}
