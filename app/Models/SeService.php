<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SeService extends Model
{
    use HasFactory;

    protected $table = 'services';

    public function scServices() {
        return $this->hasMany(ScService::class);
    }

    public function service_mapping() {
        return $this->belongsTo(ServiceMapping::class);
    }
}
