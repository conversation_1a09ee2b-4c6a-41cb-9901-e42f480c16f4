<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class Shipment extends Model
{

    // public function __construct()
    // {
    //     parent::__construct();
    //     if (config('app.env') == 'dev') {
    //         $this->table = config('database')['connections']['production_db']['database'] . '.shipments';
    //     }
    // }
    public $guarded = [];

    public static $has_access = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        'm<PERSON><EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function failedApiRequest()
    {
        return $this->belongsTo(FailedApiRequest::class, 'order_id', 'order_id');
    }
}
