<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingStatistics extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();
        static::saving(function ($shippingSatistic) {
            if ($shippingSatistic->isDirty('box_id')) {
                if ($box = ShippingBox::find($shippingSatistic->box_id)) {
                    $shippingSatistic->width = $box->width;
                    $shippingSatistic->length = $box->depth;
                    $shippingSatistic->height = $box->height;
                }
            }
        });

        static::updating(function ($shippingSatistic) {
            if ($shippingSatistic->locked == true  && !auth()->user()) {
                return false;
            }
        });
    }
}
