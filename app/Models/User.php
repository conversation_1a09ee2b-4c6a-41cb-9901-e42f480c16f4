<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use betterapp\LaravelDbEncrypter\Traits\EncryptableDbAttribute;

class User extends Authenticatable
{
    use HasFactory, Notifiable, EncryptableDbAttribute;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /** @var array The attributes that should be encrypted/decrypted */
    protected $encryptable = [
        'SellerCloudPassword',
    ];

    public function shipping_boxes()
    {
        return $this->belongsToMany(ShippingBox::class, 'shipping_box_users', 'user_id', 'shipping_box_id')->orderBy('preset_name')->withTimestamps();
    }

    public function shipments()
    {
        return $this->hasMany(Shipment::class);
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function getUseQZAttribute()
    {
        return request()->cookie('useQZ');
    }
}
