{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "betterapp/laravel-db-encrypter": "^1.0", "campaignmonitor/createsend-php": "^6.1", "doctrine/dbal": "^2.12", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^8.0", "laravel/horizon": "^5.7", "laravel/tinker": "^2.0", "laravel/ui": "^3.1", "league/flysystem-aws-s3-v3": "^1.0", "livewire/livewire": "2.5.5", "predis/predis": "^1.1", "spatie/laravel-backup": "6.16"}, "require-dev": {"facade/ignition": "^2.3.6", "laravel/telescope": "^4.7", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "autoload": {"files": ["app/Http/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}