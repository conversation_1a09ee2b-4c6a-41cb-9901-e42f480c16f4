<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMoreUserFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('ScannerId');
            $table->Integer('ScaleId')->nullable();
            $table->Integer('ComputerId')->nullable();
            $table->string('SellerCloudUsername', 256)->nullable();
            $table->string('SellerCloudPassword', 256)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->Integer('ScannerId')->nullable();
            $table->dropColumn(['ComputerId', 'ScaleId', 'SellerCloudUsername', 'SellerCloudPassword']);
        });
    }
}
