<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingBoxesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_boxes', function (Blueprint $table) {
            $table->id();
            $table->integer('seller_cloud_id')->nullable();
            $table->string('preset_name');
            $table->string('width');
            $table->string('height');
            $table->string('depth');
            $table->timestamps();
        });

        Schema::create('shipping_box_users', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('shipping_box_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();
            $table->timestamps();

            $table->foreign('user_id')
                ->references('id')
                ->on('users');
             $table->foreign('shipping_box_id')
                ->references('id')
                ->on('shipping_boxes');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists(['shipping_boxes', 'shipping_box_users']);
    }
}
