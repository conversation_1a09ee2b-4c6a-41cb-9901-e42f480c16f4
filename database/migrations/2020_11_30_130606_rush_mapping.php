<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RushMapping extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('SellerCloudPassword', 1024)->change();
        });

        Schema::create('service_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::create('carriers', function (Blueprint $table) {
            $table->id();
            $table->string('carrier_id')->unique();
            $table->string('carrier_code');
            $table->string('account_number');
            $table->string('requires_funded_amount');
            $table->string('balance');
            $table->string('nickname')->nullable();
            $table->string('friendly_name')->nullable();
            $table->string('logo')->nullable();
            $table->boolean('primary');
            $table->boolean('has_multi_package_supporting_services');
            $table->boolean('supports_label_messages');
            $table->boolean('active');
            $table->timestamps();
        });

        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('carrier_id');
            $table->foreignId('service_mapping_id')->nullable()->constrained();
            $table->string('service_code');
            $table->string('name');
            $table->boolean('active')->default(1);
            $table->boolean('domestic');
            $table->boolean('international');
            $table->boolean('is_multi_package_supported');
            $table->timestamps();

            $table->unique(['carrier_id', 'service_code']);
            $table->foreign('carrier_id')->references('carrier_id')->on('carriers');
        });

        Schema::create('package_types', function (Blueprint $table) {
            $table->id();
            $table->string('carrier_id');
            $table->string('package_id')->nullable();
            $table->string('package_code');
            $table->string('name');
            $table->string('description');
            $table->timestamps();

            $table->unique(['carrier_id', 'package_code']);
            $table->foreign('carrier_id')->references('carrier_id')->on('carriers');
        });

        Schema::create('sc_shipping_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_mapping_id')->constrained();
            $table->string('sellercloud_name')->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('SellerCloudPassword', 256)->change();
        });

        Schema::dropIfExists('sc_shipping_services');
        Schema::dropIfExists('package_types');
        Schema::dropIfExists('services');
        Schema::dropIfExists('service_mappings');
        Schema::dropIfExists('carriers');
    }
}
