<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToShipment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->boolean('use_for_prime_rush')->default(1);
        });

        Schema::table('shipments', function (Blueprint $table) {
            $table->string('status');
            $table->string('shipment_id');
            $table->string('ship_date');
            $table->string('se_create_date');
            $table->string('shipment_cost');
            $table->string('tracking_number');
            $table->string('carrier_id');
            $table->string('service_code');
            $table->string('package_code');
            $table->boolean('voided');
            $table->string('carrier_code');
            $table->string('pdf');
            $table->string('zpl');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropColumn('use_for_prime_rush');
        });

        Schema::table('shipments', function (Blueprint $table) {
            $table->dropColumn(['status', 'shipment_id', 'ship_date', 'se_create_date', 'shipment_cost', 'tracking_number', 'carrier_id', 'service_code', 'package_code', 'voided', 'carrier_code', 'pdf', 'zpl']);
        });
    }
}
