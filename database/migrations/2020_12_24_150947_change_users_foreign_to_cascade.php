<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeUsersForeignToCascade extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shipping_box_users', function (Blueprint $table) {
            $table->dropForeign('shipping_box_users_user_id_foreign');
            $table->foreign('user_id')
            ->references('id')->on('users')
            ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shipping_box_users', function (Blueprint $table) {
            $table->dropForeign('shipping_box_users_user_id_foreign');
            $table->foreign('user_id')
            ->references('id')->on('users');
        });
    }
}
