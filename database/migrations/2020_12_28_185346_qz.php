<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Qz extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('QZPrinter', 1024)->nullable();
            $table->string('QZScaleVendorId', 1024)->nullable();
            $table->string('QZScaleProductId', 1024)->nullable();
            $table->string('QZInterface', 1024)->nullable();
            $table->string('QZEndpoint', 1024)->nullable();
            $table->boolean('useQZ')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('QZPrinter');
            $table->dropColumn('QZScaleVendorId');
            $table->dropColumn('QZScaleProductId');
            $table->dropColumn('QZInterface');
            $table->dropColumn('QZEndpoint');
            $table->dropColumn('useQZ');
        });
    }
}
