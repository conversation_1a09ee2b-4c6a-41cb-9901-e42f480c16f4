<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MapScServices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sc_shipping_services', function (Blueprint $table) {
            $table->foreignId('service_id')->nullable()->constrained();
            // $table->dropForeign(['service_mapping_id']);
            // $table->dropColumn('service_mapping_id');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->dropForeign(['carrier_id']);
            $table->renameColumn('carrier_id', 'carrier_code');
        });

        Schema::table('package_types', function (Blueprint $table) {
            $table->dropForeign(['carrier_id']);
            $table->renameColumn('carrier_id', 'carrier_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sc_shipping_services', function (Blueprint $table) {
            $table->dropForeign(['service_id']);
            $table->dropColumn('service_id');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->renameColumn('carrier_code', 'carrier_id');
            $table->foreign('carrier_id')->references('carrier_id')->on('carriers');

        });

        Schema::table('package_types', function (Blueprint $table) {
            $table->renameColumn('carrier_code', 'carrier_id');
            $table->foreign('carrier_id')->references('carrier_id')->on('carriers');
        });
    }
}
