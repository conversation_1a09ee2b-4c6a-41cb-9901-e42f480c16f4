<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('carriers', function (Blueprint $table) {
            $table->index('carrier_code');
            $table->index('active');
        });

        Schema::table('package_types', function (Blueprint $table) {
            $table->index('package_id');
            $table->index('name');
        });

        Schema::table('roles', function (Blueprint $table) {
            $table->index('name');
        });

        Schema::table('sc_shipping_services', function (Blueprint $table) {
            $table->index('service_mapping_id');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->index('name');
            $table->index('active');
            $table->index('use_for_prime_rush');
        });

        Schema::table('service_mappings', function (Blueprint $table) {
            $table->index('name');
        });

        Schema::table('shipments', function (Blueprint $table) {
            // $table->index('label_id');
            $table->index('order_id');
            // $table->index('user_id');
            // $table->index('external_order_id');
            // $table->index('status');
            // $table->index('shipment_id');
            // $table->index('tracking_number');
            // $table->index('carrier_id');
            // $table->index('service_code');
            // $table->index('package_code');
            $table->index('voided');
            // $table->index('carrier_code');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('carriers', function (Blueprint $table) {
            $table->dropIndex('carrier_code');
            $table->dropIndex('active');
        });

        Schema::table('package_types', function (Blueprint $table) {
            $table->dropIndex('package_id');
            $table->dropIndex('name');
        });

        Schema::table('roles', function (Blueprint $table) {
            $table->dropIndex('name');
        });

        Schema::table('sc_shipping_services', function (Blueprint $table) {
            $table->dropIndex('service_mapping_id');
        });

        Schema::table('services', function (Blueprint $table) {
            $table->dropIndex('name');
            $table->dropIndex('active');
            $table->dropIndex('use_for_prime_rush');
        });

        Schema::table('service_mappings', function (Blueprint $table) {
            $table->dropIndex('name');
        });

        Schema::table('shipments', function (Blueprint $table) {
            $table->dropIndex('order_id');
            $table->dropIndex('voided');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('name');
        });
    }
}
