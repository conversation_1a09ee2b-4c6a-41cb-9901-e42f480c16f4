<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MakeColumnNullable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sc_shipping_services', function (Blueprint $table) {
            $table->unsignedBigInteger('service_mapping_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sc_shipping_services', function (Blueprint $table) {
            $table->unsignedBigInteger('service_mapping_id')->change();
        });
    }
}
