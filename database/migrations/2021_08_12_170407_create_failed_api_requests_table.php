<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFailedApiRequestsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('failed_api_requests', function (Blueprint $table) {
            $table->id();
            $table->string('order_id');
            $table->string('type')->nullable();
            $table->json('order')->nullable();
            $table->json('label')->nullable();
            $table->text('error')->nullable();
            $table->string('status')->nullable();
            $table->string('sortbox_id')->nullable();
            $table->string('resolved_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('failed_api_requests');
    }
}
