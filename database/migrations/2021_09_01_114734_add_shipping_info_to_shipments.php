<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShippingInfoToShipments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shipments', function (Blueprint $table) {
            $table->string('skus', 1024)->nullable();
            $table->float('weight')->nullable();
            $table->float('length')->nullable();
            $table->float('height')->nullable();
            $table->float('width')->nullable();
            $table->unsignedInteger('box_id')->nullable();
            $table->string('sortbox_crc')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shipments', function (Blueprint $table) {
            $table->dropColumn('skus');
            $table->dropColumn('weight');
            $table->dropColumn('length');
            $table->dropColumn('height');
            $table->dropColumn('width');
            $table->dropColumn('box_id');
        });
    }
}
