{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"axios": "^0.19", "cross-env": "^7.0", "laravel-mix": "^5.0.1", "lodash": "^4.17.19", "resolve-url-loader": "^3.1.0", "sass": "^1.29.0", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.12"}, "dependencies": {"@tailwindcss/ui": "^0.6.2", "qz-tray": "^2.1.2", "sha": "^3.0.0", "tailwindcss": "^1.9.6", "ws": "^7.4.1"}}