@tailwind base;
@tailwind components;
@tailwind utilities;

.btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base leading-6 font-medium rounded-md text-white bg-indigo-600 transition ease-in-out duration-150;
}

.btn-secondary {
    @apply text-indigo-700 bg-transparent border-indigo-600;
}

.btn-sm {
    @apply px-4 py-1;
}

.btn-loading {
    @apply bg-gray-200;
}

.btn:hover {
    @apply bg-indigo-500;
}

.btn-secondary:hover {
    @apply text-white;
}

.btn:active {
    @apply bg-indigo-700;
}

.btn:focus {
    @apply outline-none border-indigo-700 shadow-outline-indigo;
}
