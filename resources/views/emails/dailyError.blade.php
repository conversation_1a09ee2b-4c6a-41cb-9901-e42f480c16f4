<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,800,800i,900,900i" rel="stylesheet">
    <title>Order Created</title>
    <style>
        body {
            background-color: #eef1f4;
            font-size: 16px;
            font-family: Nunito, system-ui, BlinkMacSystemFont, -apple-system, sans-serif;
        }

        .container {
            margin-top: 5rem;
        }

        /* .form {
            max-width: 22rem;
            margin: auto;
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .05);
        } */

        input {
            display: block;
            border: 1px solid #bacad6;
            width: 100%;
            height: 2.25rem;
            border-radius: 8px;
            padding-left: .75rem;
            padding-right: .75rem;
            outline: 0;
            margin-top: .25rem;
            font-size: inherit;
            font-family: inherit;
        }

        input:focus,
        input:active,
        button:hover,
        button:focus {
            box-shadow: 0 0 0 3px rgba(64, 153, 222, .5);
        }

        button {
            border: transparent;
            background-color: #4099de;
            width: 100%;
            display: block;
            border-radius: 8px;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .05);
            text-shadow: 0 1px 2px rgba(0, 0, 0, .2);
            color: white;
            margin-top: 2rem;
            padding: .60rem;
            cursor: pointer;
            font-size: inherit;
            font-family: inherit;
        }

        label,
        button {
            font-weight: 800;
        }
    </style>
</head>

<body>
    @foreach($issues as $issue)
    <div style="margin:10; padding-left:20;border-style: solid;  max-width:200; ">
        <p style="margin-left:15;">
            Error: {{$issue['issue']}}
        </p>
        <p>
            Order: {{$issue['order_id']}}
        </p>
        <p>
            Shipper: {{$issue['shipper']}}
        </p>
        <p>
            <a href="{{$issue['link']}}"> View Issue</a>
        </p>
    </div>
    @endforeach
    <!-- // 'listings with quantity and variations' => $order->shipments->map(function ($shipment) {
                 return [
                 'listings' => $shipment->details->map->meta,
                 // 'listings' => $shipment->details->map(function ($detail) {
                 // return array_merge($detail->listing->front_end, ['price' => $detail->meta->price, 'spec' => $detail->listing->variationString(), 'quantity' => $detail->quantity]);
                 // }),
                 'estimated_delivery_date' => $shipment->arrives_at
                 ];
                 }),
                 'payment details' => $order->payments()->get()->map->front_end,
                 //TODO
                 'link to view in acount if has account',
                 'app_url' => config('app.front_end_url'), -->


</body>

</html>