<div class="h-screen flex overflow-hidden bg-gray-100">
  <!-- Off-canvas menu for mobile, show/hide based on off-canvas menu state. -->
  <div class="md:hidden">
    <div class="fixed inset-0 flex z-40">
      <!--
          Off-canvas menu overlay, show/hide based on off-canvas menu state.
  
          Entering: "transition-opacity ease-linear duration-300"
            From: "opacity-0"
            To: "opacity-100"
          Leaving: "transition-opacity ease-linear duration-300"
            From: "opacity-100"
            To: "opacity-0"
        -->
      <div class="fixed inset-0">
        <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
      </div>
      <!--
          Off-canvas menu, show/hide based on off-canvas menu state.
  
          Entering: "transition ease-in-out duration-300 transform"
            From: "-translate-x-full"
            To: "translate-x-0"
          Leaving: "transition ease-in-out duration-300 transform"
            From: "translate-x-0"
            To: "-translate-x-full"
        -->
      <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
        <div class="absolute top-0 right-0 -mr-14 p-1">
          <button class="flex items-center justify-center h-12 w-12 rounded-full focus:outline-none focus:bg-gray-600" aria-label="Close sidebar">
            <svg class="h-6 w-6 text-white" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
          <div class="flex-shrink-0 flex items-center px-4">
            <a href="/">
              <img class="h-8 w-auto" src="{{config('app.url')}}/logo.png" alt="Workflow">
            </a>
          </div>
          <livewire:menu />
        </div>
        <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
          <!-- Authentication Links -->
          @guest
          @if (Route::has('login'))
          <li class="nav-item">
            <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
          </li>
          @endif

          @if (Route::has('register'))
          <li class="nav-item">
            <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
          </li>
          @endif
          @else
          <a href="#" class="flex-shrink-0 group block focus:outline-none">
            <div class="flex items-center">
              <div>
                <a href="/user">
                  <img class="inline-block h-10 w-10 rounded-full" src="https://pkimgcdn.peekyou.com/3f62d01c7ca3ff808db7eb921d934d28.jpeg" alt="">
                </a>
              </div>
              <div class="ml-3">
                <p class="text-base leading-6 font-medium text-gray-700 group-hover:text-gray-900">
                  <a href="/user">
                    {{ Auth::user()->name }}
                  </a>
                </p>
                <p class="text-sm leading-5 font-medium text-gray-500 group-hover:text-gray-700 group-focus:underline transition ease-in-out duration-150">
                  <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault();
                                        document.getElementById('logout-form').submit();">
                    {{ __('Logout') }}
                  </a>

                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                  @csrf
                </form>
                </p>
              </div>
            </div>
          </a>
          @endguest
        </div>
      </div>
      <div class="flex-shrink-0 w-14">
        <!-- Force sidebar to shrink to fit close icon -->
      </div>
    </div>
  </div>

  <!-- Static sidebar for desktop -->
  <div class="hidden md:flex md:flex-shrink-0">
    <div class="flex flex-col w-64">
      <!-- Sidebar component, swap this element with another sidebar if you like -->
      <div class="flex flex-col h-0 flex-1 border-r border-gray-200 bg-white">
        <div class="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
          <div class="flex items-center flex-shrink-0 px-4">
            <a href="/">
              <img class="h-8 w-auto" src="{{config('app.url')}}/logo.png" alt="Workflow">
            </a>
          </div>
          <livewire:menu />
        </div>
        <!-- <div class="flex-shrink-0  border-t border-gray-200 p-4"> -->
        <div class="m-2 p-2 overflow-hidden border border-gray-300 sm:rounded-lg">
          <div>
            <span data-hj-allow class="text-sm text-gray-900 ml-2 mb-1" id="date" style="font-family: 'Roboto', sans-serif;"></span>
            <span data-hj-allow class="text-sm text-gray-900 mb-1" id="time" style="font-family: 'Roboto', sans-serif;"></span>
          </div>


          <div data-hj-allow class="ml-2 text-xs font-medium text-gray-500">Computer</div>
          <div data-hj-allow class="ml-2 mb-1 text-xs text-gray-900" id="computer"></div>

          <div data-hj-allow class="ml-2 text-xs font-medium text-gray-500 ">Label Printer</div>
          <div data-hj-allow class="ml-2 mb-1 text-xs text-gray-900" id="label_printer"></div>

          <div data-hj-allow class="ml-2 text-xs font-medium text-gray-500 ">Card Printer</div>
          <div data-hj-allow class="ml-2 mb-1 text-xs text-gray-900" id="card_printer"></div>

          <div data-hj-allow class="ml-2 text-xs font-medium text-gray-500 ">Invoice Printer</div>
          <div data-hj-allow class="ml-2 mb-1 text-xs text-gray-900" id="invoice_printer"></div>

          <div data-hj-allow class="ml-2 text-xs font-medium text-gray-500">Scale</div>
          <div data-hj-allow class="ml-2 mb-2 text-xs text-gray-900" id="scale"></div>
        </div>
        <div class="flex-shrink-0  border-t border-gray-200 p-4">

          @guest
          @if (Route::has('login'))
          <a href="{{ route('login') }}">{{ __('Login') }}</a>
          @endif

          @if (Route::has('register'))
          <a href="{{ route('register') }}">{{ __('Register') }}</a>
          @endif
          @else
          <div class="flex items-center">
            <div>
              <a href="/user">
                @php
                $words = explode(' ', Auth::user()->name);
                $initials = strtoupper(substr($words[0], 0, 1) . substr(end($words), 0, 1));
                @endphp
                <div class="flex items-center justify-center flex-shrink-0 h-10 w-10 rounded-full text-white bg-blue-500">
                  <span>{{ $initials }}</span>
                </div>
                {{-- <img class="inline-block h-9 w-9 rounded-full" src="https://pkimgcdn.peekyou.com/3f62d01c7ca3ff808db7eb921d934d28.jpeg" alt=""> --}}
              </a>
            </div>
            <div class="ml-3">
              <p class="text-sm leading-5 font-medium text-gray-700 group-hover:text-gray-900">
                <a href="/user">
                  {{ Auth::user()->name }}
                </a>
              </p>
              <p class="text-sm leading-5 font-medium text-gray-500 group-hover:text-gray-700 group-focus:underline transition ease-in-out duration-150">
                <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault();
                                      document.getElementById('logout-form').submit();">
                  {{ __('Logout') }}
                </a>

              <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                @csrf
              </form>
              </p>
            </div>
          </div>
          @endguest
        </div>
      </div>
    </div>
  </div>

  <script>
    function currentTime() {
      let fullDate = new Date();
      let mn = fullDate.getMonth() + 1;
      let dm = fullDate.getDate();
      let yr = fullDate.getFullYear().toString().substr(-2);
      let hh = fullDate.getHours();
      let mm = fullDate.getMinutes();
      let ss = fullDate.getSeconds();
      let session = "AM";

      if (hh == 0) {
        hh = 12;
      }
      if (hh > 12) {
        hh = hh - 12;
        session = "PM";
      }

      mn = (mn < 10) ? "0" + mn : mn;
      dm = (dm < 10) ? "0" + dm : dm;
      hh = (hh < 10) ? "0" + hh : hh;
      mm = (mm < 10) ? "0" + mm : mm;
      ss = (ss < 10) ? "0" + ss : ss;

      let date = mn + "/" + dm + "/" + yr;
      let time = hh + ":" + mm + ":" + ss;
      // let time = mn + "/" + dm + "/" + yr + " " + hh + ":" + mm + ":" + ss + " " + session;

      document.getElementById("time").innerText = time;
      document.getElementById("date").innerText = date;
      let t = setTimeout(function() {
        currentTime()
      }, 1000);
    }
    currentTime();

    async function getUserInfo() {
      let response = await fetch('/user-info');
      let json = await response.json();
      let invoicePrinter = json.invoice_printer?. [0];
      let cardPrinter = json.card_printer?. [0];
      let labelPrinter = json.label_printer?. [0];
      let scales = json.scales;
      let computer = json.computer?. [0];

      if (invoicePrinter) {
        if (invoicePrinter?.state == 'offline') {
          document.getElementById("invoice_printer").innerText = 'Invoice printer is offline';
          document.getElementById("invoice_printer").style = "color:red;";
        } else {
          document.getElementById("invoice_printer").innerText = invoicePrinter?.name + ' - ' + invoicePrinter?.id + ' - ' + invoicePrinter?.computer?.name;
          document.getElementById("invoice_printer").style = "";
        }
      } else {
        document.getElementById("invoice_printer").innerText = 'No invoice printer selected';
        document.getElementById("invoice_printer").style = "color:red;";
      }

      if (labelPrinter) {
        if (labelPrinter?.state == 'offline') {
          document.getElementById("label_printer").innerText = 'Label printer is offline';
          document.getElementById("label_printer").style = "color:red;";
        } else {
          document.getElementById("label_printer").innerText = labelPrinter?.name + ' - ' + labelPrinter?.id + ' - ' + labelPrinter?.computer?.name;
          document.getElementById("label_printer").style = "";
        }
      } else {
        document.getElementById("label_printer").innerText = 'No label printer selected';
        document.getElementById("label_printer").style = "color:red;";
      }

      if (cardPrinter) {
        if (cardPrinter?.state == 'offline') {
          document.getElementById("card_printer").innerText = 'Card printer is offline';
          document.getElementById("card_printer").style = "color:red;";
        } else {
          document.getElementById("card_printer").innerText = cardPrinter?.name + ' - ' + cardPrinter?.id + ' - ' + cardPrinter?.computer?.name;
          document.getElementById("card_printer").style = "";
        }
      } else {
        document.getElementById("card_printer").innerText = 'No card printer selected';
        document.getElementById("card_printer").style = "color:red;";
      }

      if (computer) {
        document.getElementById("computer").innerText = computer?.name;
        document.getElementById("computer").style = "";

      } else {
        document.getElementById("computer").innerText = 'No computer selected';
        document.getElementById("computer").style = "color:red;";
      }

      if (scales?. [0]) {
        document.getElementById("scale").style = "";
        document.getElementById("scale").innerText = scales?. [0]?.productId + ' - ' + scales?. [0]?.deviceName;
      } else {
        document.getElementById("scale").innerText = 'Scale is offline';
        document.getElementById("scale").style = "color:red;";

      }
      let t = setTimeout(function() {
        getUserInfo()
      }, 60000);
    }
    getUserInfo();
  </script>