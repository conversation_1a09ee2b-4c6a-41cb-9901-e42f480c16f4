<!DOCTYPE html>
<html lang="en">
@include('includes.head')

<body>
    @include('includes.header')

    <div class="flex flex-col w-0 flex-1 overflow-hidden">
        <div class="md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3">
            <button class="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:bg-gray-200 transition ease-in-out duration-150" aria-label="Open sidebar">
                <!-- Heroicon name: menu -->
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <main class="flex-1 relative z-0 overflow-y-auto focus:outline-none" tabindex="0">
            <div class="pt-2 pb-6 md:py-6">
                <div class="mx-auto px-4 sm:px-6 md:px-8">
                    <div class="p-16 bg-white overflow-hidden rounded-lg">
                        <h1 class="text-2xl font-semibold text-gray-900">@yield('title')</h1>
                        @yield('content')
                    </div>
                </div>
            </div>
        </main>
    </div>

    @include('includes.footer')

    @livewireScripts
    <script type="text/javascript" src="/js/qz-tray.js"></script>
    <script>
        console.log.apply(console, ["%c Fabble Shipping Software Developed by Efraim Wachsman %c ************ %c https://efraimw.com", "display:block; padding:5px; background: #222; line-height:40px; color:#fff;", "display:block; padding:5px; background: #4099FF; line-height:40px; color:#fff;", "display:block; padding:5px; background: #E8E7E7; line-height:40px;"]);

        function QZConnect() {
            return new Promise(function(resolve, reject) {
                if (qz.websocket.isActive()) { // if already active, resolve immediately
                    resolve();
                } else {
                    qz.security.setCertificatePromise(function(resolve, reject) {
                        fetch("/qz/digital-certificate.txt", {
                                cache: 'no-store',
                                headers: {
                                    'Content-Type': 'text/plain'
                                }
                            })
                            .then(function(data) {
                                data.ok ? resolve(data.text()) : reject(data.text());
                            });
                    });

                    qz.security.setSignatureAlgorithm("SHA512");
                    qz.security.setSignaturePromise(function(toSign) {
                        return function(resolve, reject) {
                            fetch("/api/qz/sign/" + toSign, {
                                    cache: 'no-store',
                                    headers: {
                                        'Content-Type': 'text/plain'
                                    }
                                })
                                .then(function(data) {
                                    data.ok ? resolve(data.text()) : reject(data.text());
                                });
                        };
                    });

                    // try to connect once before firing the mimetype launcher
                    qz.websocket.connect().then(resolve, function retry() {
                        // if a connect was not succesful, launch the mimetime, try 3 more times
                        window.location.assign("qz:launch");
                        qz.websocket.connect({
                            retries: 2,
                            delay: 1
                        }).then(resolve, reject);
                    });
                }
            });
        }

        function Tprint(url) {
            var printWindow = window.open(url, 'Print')
            printWindow.addEventListener('load', function() {
                printWindow.print();
            }, true);
        };

        function QZPrint(url, evenIfNotUsing = false) {
            return Tprint(url);
            if (evenIfNotUsing) {
                QZConnect().then(function() {
                    var config = qz.configs.create('{{auth()->user()->QZPrinter}}');
                    fetch('/api/download/' + btoa(url), {
                            method: "get",
                        })
                        .then((data) => {
                            if (data.status == 200) {
                                data.text().then(function(text) {
                                    qz.print(config, [text]).then(function() {
                                        console.log("Sent data to printer");
                                    });
                                });

                            } else {
                                alert("Error getting zpl data");
                            }
                        })
                        .catch(function(error) {
                            console.log(error);
                        });
                });
            }
        }

        /** Attempts to parse scale reading from USB raw output */
        function readScaleData(data) {
            // Filter erroneous data
            if (!data || data.length < 4 || data.slice(2, 8).join('') == "000000000000") {
                return null;
            }

            // Get status
            var status = parseInt(data[1], 16);
            switch (status) {
                case 1: // fault
                case 5: // underweight
                case 6: // overweight
                case 7: // calibrate
                case 8: // re-zero
                    status = 'Error';
                    break;
                case 3: // busy
                    status = 'Busy';
                    break;
                case 2: // stable at zero
                case 4: // stable non-zero
                default:
                    status = 'Stable';
            }

            // Get precision
            var precision = parseInt(data[3], 16);
            precision = precision ^ -256; //unsigned to signed

            // xor on 0 causes issues
            if (precision == -256) {
                precision = 0;
            }

            // Get weight
            data.splice(0, 4);
            data.reverse();
            var weight = parseInt(data.join(''), 16);

            weight *= Math.pow(10, precision);
            weight = weight.toFixed(Math.abs(precision));

            // Get units
            var units = parseInt(data[2], 16);
            switch (units) {
                case 2:
                    units = 'g';
                    weight = weight / 453.592;
                    break;
                case 3:
                    units = 'kg';
                    weight = weight / 0.453592;
                    break;
                case 11:
                    units = 'oz';
                    weight = weight / 16;
                    break;
                case 12:
                default:
                    units = 'lbs';
            }

            // get pound & ounces
            // var lb = Math.floor(weight);
            // var oz = Math.round(Math.max(0, (weight - lb) * 16), 1);

            return weight;
        }
    </script>
    <!-- Hotjar Tracking Code for ship.p4less.com -->
    <script type="text/javascript">

        window._mfq = window._mfq || [];
        setTimeout((function() {
            var mf = document.createElement("script");
            mf.type = "text/javascript"; 
            mf.defer = true;
            mf.src = "//cdn.mouseflow.com/projects/421c0038-1120-4cc3-8dc1-57026b3f30cc.js";
            document.getElementsByTagName("head")[0].appendChild(mf);
        })(), 80000);


        // (function(h, o, t, j, a, r) {
        //     h.hj = h.hj || function() {
        //         (h.hj.q = h.hj.q || []).push(arguments)
        //     };
        //     h._hjSettings = {
        //         hjid: 2514252,
        //         hjsv: 6
        //     };
        //     a = o.getElementsByTagName('head')[0];
        //     r = o.createElement('script');
        //     r.async = 1;
        //     r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        //     a.appendChild(r);
        // })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>
</body>

</html>