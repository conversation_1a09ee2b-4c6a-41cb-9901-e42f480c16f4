<tr>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="flex items-center">
            <div class="ml-4">
                <div class="text-sm leading-5 text-gray-900 font-medium">
                    {{$issue->error}}
                </div>
                <div class="text-sm leading-5 font-medium text-gray-500 pt-1">
                    <a target="_blank" href="https://pfl.cwa.sellercloud.com/orders/Orders_details.aspx?ID={{ $issue->order_id }}">Order ID: {{ $issue->order_id }}</a>
                </div>
            </div>
        </div>
    </td>
    <!-- <td class="px-6 py-4 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{ data_get($issue,'shipment.service_code')}} - ${{data_get($issue,'shipment.shipment_cost')}}</div>
        <div class="text-sm leading-5 text-gray-500">{{data_get($issue,'shipment.carrier_code')}} - {{data_get($issue,'label.label_id')}}</div>
    </td> -->
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{ date('M j, Y \a\t g:i a', strtotime(data_get($issue,'shipment.se_create_date'))) }}</div>
        @if( data_get($issue,'shipment.user'))<div class="text-sm leading-5 text-gray-500">{{ $issue->shipment->user['email'] }}</div>@endif
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        @switch($issue->status)
        @case('manual_resolved')
        @case('Resolved')
        @php
        $color = 'green';
        @endphp
        @break
        @case('Unresolved')
        @php
        $color = 'red';
        @endphp

        @endswitch
        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{$color}}-100 text-{{$color}}-800">
            {{ $issue->status == 'manual_resolved' ? 'Manual Resolved':$issue->status }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        @if($issue->resolved_by)
        <div class="text-sm leading-5 text-gray-500">{{ $issue->resolved_by }}</div>
        <div class="text-sm leading-5 text-gray-500">{{ date('M j, Y \a\t g:i a', strtotime(data_get($issue,'updated_at'))) }}</div>
        @endif
    </td>
    <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 font-medium">
        @if(data_get($issue,'shipment.pdf'))
        @if(auth()->user()->useQZ)
        <button onclick="QZPrint('{{$issue->shipment->zpl}}')" class="btn dtn-secondary btn-sm {{$issue->status =='Unresolved' ?: 'opacity-50'}}" {{$issue->status =='Unresolved' ?: 'disabled'}}>Reprint</button>
        @else
        <button wire:click="shipLocal('{{$issue->shipment->pdf}}', 'reprint: Order ID: {{$issue->order_id}}, Label ID: {{data_get( $issue,'label.label_id')}}' , {{$issue->id}})" wire:loading.class="opacity-50" wire:loading.attr="disabled" class="btn btn-secondary btn-sm {{$issue->status =='Unresolved' ?: 'opacity-50'}}" {{$issue->status =='Unresolved' ?: 'disabled'}}>
            Ship local
        </button>
        @if($issue->printer_id)
        <button wire:click="shipShipper('{{$issue->shipment->pdf}}', 'reprint: Order ID: {{$issue->order_id}}, Label ID: {{$issue->label_id}}', {{$issue->id}})" wire:loading.class="opacity-50" wire:loading.attr="disabled" class="btn btn-secondary btn-sm {{$issue->status =='Unresolved' ?: 'opacity-50'}}" {{$issue->status =='Unresolved' ?: 'disabled'}}>
            Ship on Shippers PC
        </button>
        @endif
        @endif
        @endif
        @if($issue->type!='SkuBlox')
        <button wire:click="resendSellerCloud('{{$issue->id}}')" wire:loading.class="opacity-50" wire:loading.attr="disabled" class="btn btn-sm {{$issue->status =='Unresolved' ?: 'opacity-50'}}" style="min-width: 176px;" {{$issue->status =='Unresolved' ?: 'disabled'}}>
            {{ $issue->type!='PrintNode'? "Update Sellercloud ": "Mark as printed"}}
        </button>
        @else
        <button wire:click="resendSortBox('{{$issue->id}}')" wire:loading.class="opacity-50" wire:loading.attr="disabled" class="btn btn-sm {{$issue->status =='Unresolved' ?: 'opacity-50'}}" style="min-width: 176px;" {{$issue->status =='Unresolved' ?: 'disabled'}}>
            Close Skublox Light
        </button>
        @endif
        <button wire:click="manualResolved('{{$issue->id}}')" wire:loading.class="opacity-50" wire:loading.attr="disabled" style="background-color: #31c48d; background-color: rgba(49, 196, 141, var(--bg-opacity));" class="btn btn-sm {{$issue->status =='Unresolved' ?: 'opacity-50'}}" {{$issue->status =='Unresolved' ?: 'disabled'}}>
            Manualy Resolved
        </button>
    </td>
</tr>