<div>
    <!-- {{session('message')}} -->
    @if (session()->has('message'))
    <livewire:status :status="session('message')" :statusType="'message'" />
    @endif

    @if(session()->has('error'))
    <livewire:status :status="session('error')" :statusType="'error'" />
    @endif

    {{-- <div wire:loading> {{ $loading_message }}
</div> --}}
<div class="flex justify-between mt-8">
    <div>
        <label class="block text-sm font-medium text-gray-700" for="selectedUser">Shipper</label>
        <select name="selectedUser" id="selectedUser" wire:model="user" onfocus="this.blur();" class="mt-1 mb-4 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
            <option value="">Show all</option>
            @foreach ($users as $local_user)
            <option value="{{ $local_user->id }}">{{$local_user->name}} - {{$local_user->email}}</option>
            @endforeach
        </select>
    </div>
    <div>
        <label class="block text-sm font-medium text-gray-700" for="status">Status</label>
        <select name="status" id="status" wire:model="status" onfocus="this.blur();" class="mt-1 mb-4 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
            <option value="">Show all</option>
            @foreach ($statuses as $local_status)
            <option @if($local_status==$status) selected @endif value="{{ $local_status }}">{{$local_status}}</option>
            @endforeach
        </select>
    </div>

    <div>
        <label for="search" class="block text-sm font-medium text-gray-700">search</label>
        <div class="mt-1 relative rounded-md shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <!-- Heroicon name: search -->
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
            <input type="search" name="search" id="search" wire:model="search" class="p-2 border h-12 text-3xl rounded focus:ring-indigo-500 focus:border-indigo-500 pl-10">
        </div>
    </div>
</div>

<button type="button" wire:click="resetFilter" class="btn btn-sm mt-2">
    Reset filters
</button>

<div class="flex flex-col mt-6">
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                <table class="min-w-full divide-y divide-gray-200">
                    <livewire:tablehead :columns="$columns" />
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach ($issues as $issue)
                        <livewire:issue :issue="$issue" :key="$issue->id" />
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="mt-6">
    {{ $issues->links() }}
</div>