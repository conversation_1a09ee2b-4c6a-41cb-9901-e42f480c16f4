<div>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/pikaday/css/pikaday.css">
    
    @if (session()->has('message'))
        <livewire:status :status="session('message')" :statusType="'message'" />
    @endif

    <div wire:loading wire:init="loadList">
        Loading...
    </div>
    
    <div class="border rounded-lg p-6">
        <h2>Create a manifest</h2>

        <div class="flex">
            <div class="my-4">
                <label for="date">Date</label>
                <div>
                    <input name="date" style="width: 20rem;" class="p-2 mt-1 form-select text-sm border leading-5 text-gray-900 font-medium" id="dates" wire:model="date" placeholder="Manifest Date" autocomplete="off">
                </div>
                
                {{-- <label for="date" class="block text-sm font-medium text-gray-700">Date</label>
                <div>
                    <select name="date" id="date" wire:model="date" onfocus="this.blur();" class="mt-1 mb-4 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                        @foreach ($days as $day)
                        <option @if($day == $date) selected @endif value="{{ date("c", strtotime($day)), }}">{{ $day }}</option>
                        @endforeach
                    </select>
                </div> --}}
            </div>
            
            <div class="my-4 pl-5">
                <label for="warehouseId">Warehouse</label>
                <div>
                    <select name="warehouse" id="warehouse" wire:model="warehouseId" class="mt-1 mb-4 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 sm:text-sm sm:leading-5">
                        <option value="">Select a warehouse</option>
                        @foreach ($warehouses as $warehouse)
                        <option value="{{ $warehouse['seWarehouse']}}">{{ $warehouse['name'] }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
            
        @foreach ($carriers as $carrier)
            <button class="btn mb-2 {{$warehouseId ?: 'opacity-50 cursor-not-allowed'}}" {{$warehouseId ?: 'disabled'}} wire:click="createManifest('{{$carrier['carrier_id']}}')">
                {{ $carrier['friendly_name'] }} @if($carrier['nickname'])({{ $carrier['nickname'] }})@endif
            </button>
        @endforeach

        @if($manifests && count($manifests))
            Manifest successfully created. Click to download

            @if(count($manifests) > 1)
                <p>ShipEngine splits manifests into chunks of 500 labels. Please download all {{ count($manifests) }}</p>
            @endif

            @foreach ($manifests as $manifest)
                <div class="border p-4">
                    <p>Manifest ID: {{ $manifest['manifest_id'] }}</p>
                    <p>Form ID: {{ $manifest['form_id'] }}</p>
                    <p>Created date: {{ date('M j, Y \a\t g:i a', strtotime($manifest['created_at'])) }}</p>
                    <p>Ship date: {{ gmdate('M j, Y', strtotime($manifest['ship_date'])) }}</p>
                    <p>Shipments: {{ $manifest['shipments'] }}</p>
                    <p>warehouse ID: {{ $manifest['warehouse_id'] }}</p>
                    <p>Carrier ID: {{ $manifest['carrier_id'] }}</p>
                    <a class="btn" download target="_blank" href="{{ $manifest['manifest_download']['href'] }}">
                        Download
                    </a>
                </div>
            @endforeach
        @endif
    </div>

    <div class="flex flex-col mt-6">
        <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
              <table class="min-w-full divide-y divide-gray-200">
                <livewire:tablehead :columns="$columns" />
                <tbody class="bg-white divide-y divide-gray-200" wire:poll.300000ms="loadList">
                    @foreach ($previousManifests as $previousManifest)
                        <tr>
                            <td class="px-6 py-4 whitespace-no-wrap">
                                <div class="flex items-center">
                                  <div class="ml-4">
                                      <div class="text-sm leading-5 font-medium text-gray-900">
                                        Manifest ID: {{ $previousManifest['manifest_id'] }}
                                      </div>
                                      <div class="text-sm leading-5 text-gray-500">
                                        Form ID: {{ $previousManifest['form_id'] }}
                                      </div>
                                  </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-no-wrap">
                                @if($previousManifest['carrier'])
                                    <div class="text-sm leading-5 text-gray-900 font-medium">{{ $previousManifest['carrier']['friendly_name'] }} - {{ $previousManifest['carrier']['carrier_code'] }}</div>
                                @endif
                                <div class="text-sm leading-5 text-gray-500">{{ $previousManifest['carrier_id'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-no-wrap">
                                <div class="text-sm leading-5 text-gray-900 font-medium">Created: {{ date('M j, Y \a\t g:i a', strtotime($previousManifest['created_at'])) }}</div>
                                <div class="text-sm leading-5 text-gray-500">Ship Date: {{ gmdate('M j, Y', strtotime($previousManifest['ship_date'])) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-no-wrap">
                                <div class="text-sm leading-5 text-gray-500">{{ $previousManifest['shipments'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-no-wrap">
                                <div class="text-sm leading-5 text-gray-500">{{ $previousManifest['warehouse'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-no-wrap text-right text-sm leading-5 font-medium">
                                <button class="btn btn-sm"><a download target="_blank" href="{{$previousManifest['manifest_download']['href']}}">Reprint</a></button>
                            </td>
                        </tr>
                    @endforeach
  
                </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  
    {{-- <livewire:pagination /> --}}
</div>


<script>
    const dates = document.getElementById("dates");


    dates.addEventListener("keydown", (event) => {
      event.preventDefault();
    });
    const fp = flatpickr(dates, {
      dateFormat: "D, F j, Y",
    });  // flatpickr

</script>

{{-- <script src="https://cdn.jsdelivr.net/npm/pikaday/pikaday.js"></script>
<script>
    var datePicker = new Pikaday({ 
        field: document.getElementById('datepicker'),
        defaultDate: new Date(),
        setDefaultDate: true
    });
    // datePicker.setDate(new Date());
</script> --}}