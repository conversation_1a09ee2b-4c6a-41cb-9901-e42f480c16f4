<nav class="mt-5 flex-1 px-2 bg-white space-y-1">
  @foreach ($items as $item)
  @php
  if('/' . Request::path() == $item['route'] || (Request::path() == '/' && $item['route'] == '/')) {
  $additionalClasses = 'text-gray-900 bg-gray-100 hover:bg-gray-100 focus:bg-gray-200';
  } else {
  $additionalClasses = 'text-gray-600 hover:bg-gray-50 focus:text-gray-900 focus:bg-gray-50';
  }
  @endphp
  <a href="{{ $item['route'] }}" class="group flex items-center px-2 py-2 text-sm leading-5 font-medium rounded-md focus:outline-none transition ease-in-out duration-150 {{ $additionalClasses }}">
    <!-- Heroicon name: cube -->
    <svg class="mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500 group-focus:text-gray-500 transition ease-in-out duration-150" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      {{!! $item['svg'] !!}}
    </svg>
    <span class="absolute flex justify-center items-center mb-5 mr-4 ml-4 text-xs text-danger-light text-sm font-bold" style="background-color:#e74444;border-radius:1rem;color:#fed7d7;font-size: 0.75rem;padding-left: 0.42rem;padding-right: 0.42rem; max-height:20px; max-width:20px;">
      <span style="font-size: 0.70rem;"> {{ $item['ball']}} </span>
    </span>
    {{ $item['name'] }}
  </a>
  @endforeach
</nav>