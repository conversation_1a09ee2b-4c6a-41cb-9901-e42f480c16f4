<div>
    <div class="mb-4 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="flex">
            <div class="px-4 py-2 sm:px-6">
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Sellercloud Order ID
                </p>
                <h3 class="text-lg leading-6 font-medium text-purple-600">
                    <a href="https://pfl.cwa.sellercloud.com/orders/Orders_details.aspx?ID={{ $order['OrderID'] }}"
                        target="_blank">{{ $order['OrderID'] }}</a>
                </h3>
            </div>
            <div class="ml-8 px-4 py-2 sm:px-6">
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Order Source Order ID
                </p>
                <h3 class="text-lg leading-6 font-medium text-purple-600">
                    <a href="{{ $order['OrderDetails']['OrderSourceOrderIdUrl'] }}"
                        target="_blank">{{ $order['OrderDetails']['OrderSourceOrderId'] }}</a>
                </h3>
            </div>
            @if (array_key_exists('sortBoxCRC', $order) && $order['sortBoxCRC'])
                <div class="ml-8 px-4 py-2 sm:px-6">
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        SkuBlox Bin
                    </p>
                    <div class="flex text-purple-600 cursor-pointer"
                        wire:click="flashLight({{ $order['sortBoxId'] }})">
                        <p class="text-lg leading-6 font-medium cursor-pointer text-purple-600"
                            wire:click="flashLight({{ $order['sortBoxId'] }})">
                            {{ $order['sortBoxCRC'] }}
                        </p>
                        <span class=" ml-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 cursor-pointer"
                                wire:click="flashLight({{ $order['sortBoxId'] }})" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                        </span>
                    </div>
                </div>
            @endif
        </div>
        <div class="border-t border-gray-200 px-4 py-2 sm:px-6">
            <dl class="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">
                        Customer Email
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {{ $order['OrderDetails']['CustomerEmail'] }}
                    </dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">
                        Order Date
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {{ date('M j, Y \a\t g:i a', strtotime($order['OrderDetails']['OrderDate'])) }}
                    </dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">
                        SubTotal
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        ${{ $order['TotalInfo']['SubTotal'] }}
                    </dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">
                        Shipping Weight
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {{ !!collect($order['previous_orders'])->count() ? $lb = floor(collect($order['previous_orders'])->last()['weight']/16) : $order['ShippingDetails']['EstShipWeight']['Pounds'] }} lb,
                        {{ !!collect($order['previous_orders'])->count() ? $lb = collect($order['previous_orders'])->last()['weight'] - ($lb * 16) : $order['ShippingDetails']['EstShipWeight']['Ounces'] }} oz
                    </dd>
                </div>

                @if ($order['isPrime'])
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">
                            Prime order
                        </dt>
                    </div>
                @endif

                @if ($order['ShippingDetails']['RushOrder'])
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">
                            Rush order
                        </dt>
                    </div>
                @endif

                @if ($order['ShippingDetails']['LockShippingMethod'])
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500">
                            Locked Shipping Method ({{ $order['ShippingDetails']['Service'] }})
                        </dt>
                    </div>
                @endif

                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">
                        Items
                    </dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        @foreach ($order['OrderItems'] as $item)
                            <p>{{ $item['Qty'] }} {{ $item['DisplayName'] }}</p>
                        @endforeach
                    </dd>
                </div>
                {{-- <div class="sm:col-span-2">
          <dt class="text-sm font-medium text-gray-500">
            Attachments
          </dt>
          <dd class="mt-1 text-sm text-gray-900">
            <ul class="border border-gray-200 rounded-md divide-y divide-gray-200">
              <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                <div class="w-0 flex-1 flex items-center">
                  <!-- Heroicon name: paper-clip -->
                  <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 flex-1 w-0 truncate">
                  resume_back_end_developer.pdf
                </span>
              </div>
              <div class="ml-4 flex-shrink-0">
                <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                  Download
                </a>
              </div>
            </li>
            <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
              <div class="w-0 flex-1 flex items-center">
                <!-- Heroicon name: paper-clip -->
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
              </svg>
              <span class="ml-2 flex-1 w-0 truncate">
                coverletter_back_end_developer.pdf
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                Download
              </a>
            </div>
          </li>
        </ul>
      </dd>
    </div> --}}
            </dl>
        </div>
    </div>

    <!-- remove from staging and production (only for testing purposes) -->
    @if ($order['apo_fpo'] || $order['po_box'] || $order['international'])
        <div class="mb-4 bg-white shadow overflow-hidden sm:rounded-lg px-4 py-2">
            <div class="sm:col-span-1">
                @if ($order['po_box'])
                    <dt class="text-sm font-medium text-red-700">
                        PO box order
                    </dt>

                @endif

                @if ($order['apo_fpo'])

                    <dt class="text-sm font-medium text-red-700">
                        APO / FPO order
                    </dt>

                @endif

                @if ($order['international'])

                    <dt class="text-sm font-medium text-red-700">
                        International order
                    </dt>

                @endif

                <dt class="text-sm font-medium text-gray-500 mt-1">
                    Shipping Address
                </dt>
                <dt class="text-sm font-medium text-gray-900 mt-1">
                    {{ $order['ShippingAddress']['StreetLine1'] }} {{ $order['ShippingAddress']['StreetLine2'] }}
                    {{ $order['ShippingAddress']['City'] }} {{ $order['ShippingAddress']['StateName'] }}
                    {{ $order['ShippingAddress']['CountryName'] }} {{ $order['ShippingAddress']['PostalCode'] }}
                </dt>
            </div>
        </div>
    @endif
    @if (collect($order['notes'])->count())
        <div class="mb-4 p-4 bg-yellow-100 shadow overflow-hidden sm:rounded-lg">
            <div class="sm:col-span-2 px-2">
                <dt class="text-sm font-medium text-gray-500">
                    Notes
                </dt>
                @foreach ($order['notes'] as $note)
                    <div class="flex items-center mt-1">
                        <label for="note{{ $loop->index }}" class="mr-3 text-gray-900"> {{ $loop->iteration }}:
                            {{ data_get($note, 'Note') }} </label>
                        <input type="checkbox" name="done" id="note{{ $loop->index }}" class="outline-red"
                            wire:click="toggleDone({{ $loop->index }})"
                            wire:model="order.notes.{{ $loop->index }}.done"
                            style="min-width:20px; min-height:20px;">
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
