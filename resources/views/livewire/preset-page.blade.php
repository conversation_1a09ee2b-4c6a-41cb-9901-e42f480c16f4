<!-- <div wire:init="loadData">
    <div class="flex">
        <div class="relative border rounded-lg p-4 shadow">
            <h2 class="mb-4 text-lg font-bold">Profile</h2>
            <div>
                <p>Name</p>
                <input type="text" name="name" id="name" wire:model="user.name" class="p-2 mb-4 border h-12 w-64 text-3xl rounded">
            </div>

            <div>
                <p>Email</p>
                <input type="email" name="email" id="email" wire:model="user.email" class="p-2 mb-4 border h-12 w-64 text-3xl rounded">
            </div>

            <div>
                <p>Password</p>
                <input type="password" name="psw" id="psw" wire:model="rawPassword" class="p-2 mb-4 border h-12 w-64 text-3xl rounded">
            </div>

            <div>
                <p>SellerCloud Username</p>
                <input type="text" name="SCusenme" id="SCusername" wire:model="user.SellerCloudUsername" class="p-2 mb-4 border h-12 w-64 text-3xl rounded">
            </div>

            <div>
                <p>SellerCloud Password</p>
                <input type="password" name="SCpswd" id="SCpassword" wire:model="user.SellerCloudPassword" class="p-2 mb-4 border h-12 w-64 text-3xl rounded">
            </div>

            <div class="flex items-center">
                <input type="checkBox" name="receiveNotifications" id="receiveNotifications" wire:model="user.receive_notifications" class="h-4 w-4 rounded mr-3">
                <label for="receiveNotifications">Receive Notifications</label>
            </div>

            <button class="btn btn-sm mt-2" wire:click="saveUser()">Save</button>
        </div>

        <div class="relative border rounded-lg p-4 ml-6 shadow">
            <h2 class="mb-4 text-lg font-bold">Printing Settings</h2>

            <input type="checkbox" name="useQZ" wire:model="useQZ" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded cursor-pointer">
            <label class="ml-2 font-medium text-gray-700">Use QZ</label>

            @if($useQZ)
            <div>
                <label class="block text-sm font-medium text-gray-700" for="QZPrinter">Select your printer</label>
                <select name="QZPrinter" id="QZPrinter" wire:model="selectedQZPrinter" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    @foreach ($QZPrinters as $QZPrinter)
                    <option @if($QZPrinter==$selectedQZPrinter) selected @endif value="{{ $QZPrinter }}">{{ $QZPrinter }}</option>
                    @endforeach
                </select>

                <label class="block text-sm font-medium text-gray-700" for="QZScale">Select your scale</label>
                <select name="QZScale" id="QZScale" wire:change="updateScale($event.target.value)" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    <option value="">Select device</option>
                    @foreach ($QZDevices as $key => $QZDevice)
                    <option @if($selectedQZScale && $QZDevice['vendorId']==json_decode($selectedQZScale)->vendorId && $QZDevice['productId'] == json_decode($selectedQZScale)->productId) selected @endif value="{{ json_encode($QZDevice) }}">Vendor ID: {{ $QZDevice['vendorId'] }} | Product ID: {{ $QZDevice['productId'] }} @if($QZDevice['vendorId'] == "0eb8" && $QZDevice['productId'] == "f000") (Try this first) @endif</option>
                    @endforeach
                </select>

                @if(count($QZInterfaces))
                <div>
                    <label class="block text-sm font-medium text-gray-700" for="QZInterface">Select your interface</label>
                    <select name="QZInterface" id="QZInterface" wire:model="selectedQZInterface" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                        <option value="">Select interface</option>
                        @foreach ($QZInterfaces as $QZInterface)
                        <option @if($QZInterface==$selectedQZInterface) selected @endif value="{{ $QZInterface }}">{{ $QZInterface }}</option>
                        @endforeach
                    </select>
                </div>
                @endif

                @if(count($QZEndpoints))
                <div>
                    <label class="block text-sm font-medium text-gray-700" for="QZEndpoint">Select your endpoint</label>
                    <select name="QZEndpoint" id="QZEndpoint" wire:model="selectedQZEndpoint" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                        <option value="">Select endpoint</option>
                        @foreach ($QZEndpoints as $QZEndpoint)
                        <option @if($QZEndpoint==$selectedQZEndpoint) selected @endif value="{{ $QZEndpoint }}">{{ $QZEndpoint }}</option>
                        @endforeach
                    </select>
                </div>
                @endif


                {{-- @if(count($scales))
                        <label class="block mb-2 text-sm font-medium text-gray-700" for="scale">Select your scale</label>
                        <select name="scale" id="scale" wire:model="selectedScale" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                            @foreach ($scales as $scale)
                                <option @if($scale['productId'] == $selectedScale) selected @endif value="{{ $scale['productId'] }}">{{ $scale['deviceName'] }} - {{ $scale['productId'] }}</option>
                @endforeach
                </select>
                @endif --}}

                <button class="btn btn-sm mt-2" onclick="QZPrint('https://api.printnode.com/static/test/pdf/test.pdf', true)">Print test page</button>
            </div>
            @else
            <div>
                <label class="block text-sm font-medium text-gray-700" for="computer">Select your PrintNode account</label>
                <select name="printNodeAccount" id="printNodeAccount" wire:model="selectedPrintNodeAccount" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    @foreach ($printNodeAccounts as $printNodeAccount)
                    <option @if($printNodeAccount['apiKey']==$selectedPrintNodeAccount) selected @endif value="{{ $printNodeAccount['apiKey'] }}">{{ $printNodeAccount['firstname'] }} {{ $printNodeAccount['lastname'] }} - {{ $printNodeAccount['email'] }}</option>
                    @endforeach
                </select>

                <label class="block text-sm font-medium text-gray-700" for="computer">Select your computer</label>
                <select name="computer" id="computer" wire:model="selectedComputer" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    @foreach ($computers as $computer)
                    <option @if($computer['id']==$selectedComputer) selected @endif value="{{ $computer['id'] }}">{{ $computer['name'] }} - {{ $computer['id'] }}</option>
                    @endforeach
                </select>

                <label class="block text-sm font-medium text-gray-700" for="printer">Select your printer</label>
                <select name="printer" id="printer" wire:model="selectedPrinter" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    @foreach ($printers as $printer)
                    <option @if($printer['id']==$selectedPrinter) selected @endif value="{{ $printer['id'] }}">{{ $printer['name'] }} - {{ $printer['id'] }} ({{ $printer['computer']['name'] }})</option>
                    @endforeach
                </select>


                @if(count($scales))
                <label class="block mb-2 text-sm font-medium text-gray-700" for="scale">Select your scale</label>
                <select name="scale" id="scale" wire:model="selectedScale" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    @foreach ($scales as $scale)
                    <option @if($scale['productId']==$selectedScale) selected @endif value="{{ $scale['productId'] }}">{{ $scale['deviceName'] }} - {{ $scale['productId'] }}</option>
                    @endforeach
                </select>
                @endif

                <button class="btn btn-sm mt-2" wire:click="printTest()">Print test page</button>
            </div>
            @endif
        </div>
    </div>

    <div class="relative border rounded-lg p-4 mt-6 shadow">
        <p class="block mb-3 text-sm font-medium text-gray-700">Shipping Boxes</p>
        <div class="flex flex-wrap">
            {{-- <label for="selectedShippingBoxes" class="block text-sm font-medium text-gray-700">Shipping Boxes</label> --}}
            @foreach ($shippingBoxes as $shippingBox)
            <div class="mr-4 mb-2 flex-25">
                <input type="checkbox" name="selectedShippingBoxes" wire:model="selectedShippingBoxes" value="{{$shippingBox->id}}" @if(in_array($shippingBox->id, $selectedShippingBoxes)) checked @endif class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded cursor-pointer">
                <label class="ml-2 font-medium text-gray-700">{{ $shippingBox->preset_name }} ({{$shippingBox->width}}x{{$shippingBox->height}}x{{$shippingBox->depth}})</label>
            </div>
            @endforeach
        </div>
        {{-- <button wire:loading.class="btn-loading" class="btn mt-4" wire:click="saveSelectedShippingBoxes">Save Shipping Boxes</button> --}}
    </div>
</div>

<script>
    document.addEventListener('livewire:load', function() {
        QZConnect().then(function() {
            qz.printers.find().then(function(data) {
                @this.QZPrinters = data;
                console.log("Available printers", data);
            }).catch(function(e) {
                console.error(e);
            });

            qz.usb.listDevices(false).then(function(data) {
                @this.QZDevices = data;
                console.log("Available devices", data);
            }).catch(function(e) {
                console.error(e);
            });

            if (@this.selectedQZScale) {
                getInterfaces();
            }

            if (@this.selectedQZInterface) {
                getEndpoints();
            }

            @this.on('deviceUpdated', () => {
                getInterfaces();
            })

            @this.on('interfaceUpdated', () => {
                getEndpoints();
            })
        })
    })

    function getInterfaces() {
        console.log("Selected Scale", JSON.parse(@this.selectedQZScale));
        qz.usb.listInterfaces(JSON.parse(@this.selectedQZScale)).then(function(data) {
            console.log("Available interfaces", data);
            @this.QZInterfaces = data;
        }).catch(function(e) {
            console.error(e);
        });
    }

    function getEndpoints() {
        var input = JSON.parse(@this.selectedQZScale);
        input.interface = @this.selectedQZInterface;

        qz.usb.listEndpoints(input).then(function(data) {
            console.log("Available endpoints", data);
            @this.QZEndpoints = data;
        }).catch(function(e) {
            console.error(e);
        });
    }
</script> -->