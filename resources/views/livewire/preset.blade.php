<tr>
    <td class="px-6 py-4 whitespace-no-wrap">
        @foreach($skus as $sku)
        <label for="sku" class="col-md-4 col-form-label text-md-right mt-4">{{ data_get($sku ,'0') }} : {{ data_get($sku ,'1') }}</label> <br>
        @endforeach
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        {{-- <select name="box_id" id="box_id" wire:model="preset.box_id" onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
            <option value="">Select a box</option>
            @foreach ($boxes as $box)
            <option @if($box['id']==$preset['box_id']) selected @endif value="{{ $box['id'] }}">{{ $box['preset_name'] }} ({{$box['width']}}x{{$box['height']}}x{{$box['depth']}})</option>
            @endforeach
        </select> --}}
        <input type="text" name="box" id="box" wire:model="box" class="mb-4- mr-2 border h-8 w-56 rounded text-center" style="min-width: 50px; margin-bottom:6px" disabled>

    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        <input type="text" name="weight" id="weight" wire:model="preset.weight" class="mb-4- mr-2 border h-8 w-28 rounded text-center" style="min-width: 50px; margin-bottom:6px" disabled>
        <span> oz </span>
    </td>
    <td class="px-6 py-4 ">
        <input type="checkBox" name="locked" id="locked" wire:model="preset.locked" class="h-4 w-4 rounded mr-3"  disabled>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 font-medium">
        <button class="btn btn-sm mt-2"> <a href="/presets/{{$preset->id}}">Edit</a></button>
    </td>
</tr>