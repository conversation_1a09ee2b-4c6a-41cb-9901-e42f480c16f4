<div>
    <div>
        @if (session()->has('message'))
            <livewire:status :status="session('message')" :statusType="'message'" />
        @endif

        @if (session()->has('error'))
            <livewire:status :status="session('error')" :statusType="'error'" />
        @endif
    </div>
    <div>
        @foreach($skus as $sku)
            <div wire:key="{{$loop->index}}" class="flex pt-4 pb-4">
                <div class="flex">
                    <label for="sku{{$loop->index}}" class="pr-1">Sku</label>
                    <input autocomplete="off" type="text" id="sku{{$loop->index}}" wire:model="skus.{{$loop->index}}.sku"
                        class="p-2 border h-8 w-64 text-center rounded">

                    <label for="quantity{{$loop->index}}" class="pr-1 pl-4 ">Quantity</label>
                    <input autocomplete="off" type="text" id="quantity{{$loop->index}}" wire:model="skus.{{$loop->index}}.quantity"
                        class="p-2 border h-8 w-12 text-center rounded">
                    
                </div>

                <div class="flex justify-center pt-1 pl-10 ">
                    <div class="pr-2">
                            <a class="btn" style="padding:0" wire:click="addPreset({{$loop->index}})">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </a>
                    </div>
                    
                    <div class="pr-4">
                            <a class="btn" style="padding:0" wire:click="removePreset({{$loop->index}})">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 12H6" />
                                </svg>
                            </a>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    <div class="flex pt-12">
        <div class="pt-2 pr-2">Box</div>
        <div class="whitespace-no-wrap">
            <select name="box_id" id="box_id" wire:model="box_id"  onfocus="this.blur();" class="mt-1 mb-4 form-select block pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                <option value="">Select a box</option>
                @foreach ($boxes as $box)
                <option value="{{ $box['id'] }}">{{ $box['preset_name'] }} ({{$box['width']}}x{{$box['height']}}x{{$box['depth']}})</option>
                @endforeach
            </select>
        </div>
        <div class="pt-2 pr-2 pl-6">Weight</div>
        <div class="pt-1 whitespace-no-wrap">
            <input type="text" name="weight" id="weight" wire:model="weight" class="mb-4 border h-8 w-28 rounded text-center" style="min-width: 50px; margin-bottom:6px">
            <span> Oz </span>
        </div>
    </div>
   <div class="flex pt-6">
        <div class="pr-6"> Locked</div>
        <div class="whitespace-no-wrap">
            <input type="checkBox" name="locked" id="locked" wire:model="locked" class="h-4 w-4 rounded mr-3">
        </div>
   </div >
    <div class="pl-10 pt-10">
        <button class="btn" wire:click="savePreset()"> {{$shippingStatisticId == 'new' ? 'Create Preset' : 'Update Preset' }}</button>
    </div>

</div>
