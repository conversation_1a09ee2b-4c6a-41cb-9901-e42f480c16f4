<tr>
    <td class="px-6 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{ $service->id }}</div>
    </td>
    <td class="px-6 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{ $service->sellercloud_name }}</div>
    </td>
    <td class="px-6 whitespace-no-wrap">
        <input type="checkbox" name="mapToGroup" wire:model="mapToGroup" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded cursor-pointer">
        <label class="ml-2 text-sm text-gray-700">Map to Group</label>
    </td>
    <td class="px-6 whitespace-no-wrap">
        @if($mapToGroup)

            <select name="service_mapping_id" id="service_mapping_id" wire:model="service.service_mapping_id" onfocus="this.blur();" class="my-2 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                <option value="">Select</option>
                @foreach ($serviceMappings as $serviceMapping)
                    <option @if($serviceMapping->id == $service->service_mapping_id) selected @endif value="{{$serviceMapping->id}}">{{$serviceMapping->name}}</option>
                @endforeach
            </select>

        @else

            <select name="service_id" id="service_id" wire:model="service.service_id" onfocus="this.blur();" class="my-2 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                <option value="">Select</option>
                @foreach ($seServices as $seService)
                    <option @if($service->service_id == $seService->id) selected @endif value="{{$seService->id}}">{{ $seService->carrier_code }} - {{ $seService->name }}</option>
                @endforeach
            </select>

        @endif
    </td>
</tr>