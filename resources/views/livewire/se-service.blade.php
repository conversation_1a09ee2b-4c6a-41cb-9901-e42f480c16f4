<tr>
    <livewire:tablecell :content="$service->id" />
    <livewire:tablecell :content="$service->carrier_code" />

    <td class="px-6 whitespace-no-wrap">
        <select name="service_mapping_id" wire:model="service.service_mapping_id" onfocus="this.blur();" class="my-2 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
            <option value="">Select</option>
            @foreach ($serviceMappings as $serviceMapping)
                <option @if($serviceMapping->id == $service->service_mapping_id) selected @endif value="{{$serviceMapping->id}}">{{ $serviceMapping->name }}</option>
            @endforeach
        </select>
    </td>

    <livewire:tablecell :content="$service->service_code" />
    <livewire:tablecell :content="$service->name" />

    <td class="px-6 whitespace-no-wrap text-center">
        <input type="checkbox" name="active" wire:model="service.active">
    </td>

    <td class="px-6 whitespace-no-wrap text-center">
        @if($service->carrier_code == "amazon_buy_shipping")
            <input type="checkbox" name="use_for_prime_rush" wire:model="service.use_for_prime_rush">
        @endif
    </td>
</tr>