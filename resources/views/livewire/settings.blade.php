<div>
  <div>
    <div>
      <p>ShipEngine API Key</p>
      <input type="text" name="seAPI<PERSON>ey" id="seAPIKey" wire:model="settings.seAPIKey" class="p-2 mb-4 border h-12 text-3xl rounded">
    </div>

    <div>
      <p>ShipEngine Warehouse</p>
      <input type="text" name="seWarehouse" id="seWarehouse" wire:model="settings.seWarehouse" class="p-2 mb-4 border h-12 text-3xl rounded">
    </div>

    <div>
      <p>Weight Verification Leeway (Percent Off)</p>
      <input type="number" maxlength="2" name="weight" id="weight" wire:model="settings.weightLeeway" class="p-2 mb-4 border h-12 text-3xl rounded">
    </div>

    <div>
      <button class="btn mt-2" wire:click="importCarriers()">Sync ShipEngine</button>
    </div>
  </div>

  <div class="flex flex-col mt-6">
    <p>SellerCloud Services</p>
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <livewire:tablehead :columns="$scColumns" />
            <tbody class="bg-white divide-y divide-gray-200">
              @foreach ($scServices as $scService)
              <livewire:sc-service :service="$scService" :seServices="$seServices" :serviceMappings="$serviceMappings" :key="$scService->id" />
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <div class="flex flex-col mt-6">
    <p>ShipEngine Services</p>
    <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
        <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <livewire:tablehead :columns="$seColumns" />
            <tbody class="bg-white divide-y divide-gray-200">
              @foreach ($seServices as $seService)
              <livewire:se-service :service="$seService" :serviceMappings="$serviceMappings" :key="$seService->id" />
              @endforeach
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>