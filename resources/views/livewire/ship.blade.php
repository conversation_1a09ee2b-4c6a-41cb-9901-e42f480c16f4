@inject('ShipClass', 'App\Http\Livewire\Ship')

<div class="max-w-6xl">
    {{-- @if ($status)
        <livewire:status :status="$status" :statusType="$statusType" />
    @endif --}}
    @if (session()->has('message'))
        <livewire:status :status="session('message')" :statusType="'message'" />
    @endif

    @if (session()->has('error'))
        <livewire:status :status="session('error')" :statusType="'error'" />
    @endif

    <!-- if the weight verification failes the we open a lightbox and disable the rest of the page -->
    @if ($failed)
        <style>
            .modal {
                transition: opacity 0.25s ease;
            }

            body.small-modal-active {
                overflow-x: hidden;
                overflow-y: visible !important;
            }

        </style>

        <div class="modal fixed w-full h-full top-0 left-0 flex items-center justify-center">
            <div class="modal-overlay absolute w-full h-full bg-gray-100 opacity-60"></div>

            <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">

                <!-- Add margin if you want to see some of the overlay behind the modal-->
                <div class="modal-content py-4 text-left px-6 relative">
                    <button class="modal-close absolute top-0 right-0 cursor-pointer flex flex-col items-center mt-1 mr--1 pr-2 pt-2 text-white text-sm z-50" 
                        wire:click="closeModal()">
                        <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="30" height="30"
                            viewBox="0 0 18 18">
                            <path
                                d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z">
                            </path>
                        </svg>
                        <!-- <span class="text-sm">(Esc)</span> -->
                    </button>
                    <!--Title-->
                    <div class="pb-3">
                        <p class="text-2xl font-bold">Weight Verification Failed</p>
                        <p class="text-2xl font-bold">Error de verificación de peso</p>
                    </div>

                    <!--Body-->
                    <p>Please check if you have the {{ Str::plural('item', count($this->order['OrderItems'])) }} below
                        in
                        the box and retry.</p>
                    <p>Verifique si tiene los elementos a continuación en el cuadro y vuelva a intentarlo</p>
                    <p>Estimated expected weight should be <span
                            style="color:red;">{{ optional($this->shippingStatistic)->weight }}</span> ounces.</p>
                    <div class="flex pt-6">
                        <div class="text-sm font-medium text-gray-500 w-28">
                            Image
                        </div>
                        <div class="text-sm font-medium text-gray-500 justify-start">
                            Item / Sku
                        </div>

                    </div>
                    <div class="overflow-y-auto mb-4" style="max-height:275px;">
                        @foreach ($this->order['OrderItems'] as $item)
                            <div class="flex pt-4 {{ !$loop->last ? 'border-b pb-4 ' : '' }}">
                                <div class="flex-shrink-0 flex justify-center mr-8 h-20 w-20">
                                    <img src="{{ (new App\Library\Services\SellerCloudAPI())->getProductDetails($item['ProductID'], $this->order['OrderID'])['Items'][0]['ImageUrl'] ?? '/dummy-post-horisontal.jpeg' }}"
                                        alt="image" style="max-width:100%; max-height:100%;">
                                    <!-- <img src="{{ data_get($item, 'ImageURL') != '' ? data_get($item, 'ImageURL') : '/dummy-post-horisontal.jpeg' }}" alt="image" style="width:100%;"> -->
                                </div>
                                <div class="pr-2">
                                    <p class="text-sm font-medium text-gray-500">
                                        {{ $item['DisplayName'] }}
                                    </p>
                                    <p class="text-xs font-medium text-gray-900 pt-1">
                                        {{ $item['ProductID'] }} Quantity: {{ $item['Qty'] }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="border divide-gray-100 rounded justify-center max-w-300 p-5" style="max-width:450px;">
                        <p class="text-sm text-gray-500 mb-3">If you still have a problem contact the manager.</p>
                        <p class="text-sm text-gray-500 mb-3">Si aún tiene un problema, comuníquese con el gerente</p>
                        <input type="password" name="pin" placeholder="PIN" class="p-2 border w-64 rounded"
                            maxlength="4" wire:model.defer="pin"></input>
                        <button
                            class="px-4 bg-transparent p-3 rounded-lg text-indigo-500 hover:bg-gray-100 hover:text-indigo-400 mr-2"
                            wire:click="getRates({{$simplifiedView ? true : ''}})"
                            wire:loading.class="opacity-50" wire:loading.attr="disabled">
                            Bypass - Derivación
                        </button>
                    </div>

                    <!--Footer-->
                    <div class="flex justify-end pt-2">
                        <button class="modal-close px-4 bg-indigo-500 p-3 rounded-lg text-white hover:bg-indigo-400"
                            wire:click="getRates({{$simplifiedView ? true : ''}})"
                            wire:loading.class="opacity-50" wire:loading.attr="disabled">
                            Retry - Rever
                        </button>
                    </div>

                </div>
            </div>
        </div>

        <script>
            const body = document.querySelector('body')
            body.classList.add('small-modal-active')

        </script>
    @else
        <script>
            const body = document.querySelector('body')
            body.classList.remove('small-modal-active')

        </script>
    @endif

    @if (!$order || !is_array($order) || !array_key_exists('OrderID', $order))
        <div>
            @if (!$picklist)
                <div class="loader" wire:loading>
                    Loading...
                </div>
                <div>
                    <p>Order #</p>
                    <input data-hj-allow autocomplete="off" type="text" name="orderId" id="orderId"
                        wire:change="getOrder('orderId', $event.target.value)"
                        class="p-2 mb-4 border h-12 w-64 text-3xl rounded"
                        {{ Session::get('orderType') == 'orderId' ? 'autofocus' : '' }}>
                </div>

                <div>
                    <p>Picklist</p>
                    <input data-hj-allow autocomplete="off" type="text" name="picklistId" id="picklistId"
                        wire:change="getPicklist($event.target.value)"
                        class="p-2 mb-4 border h-12 w-64 text-3xl rounded"
                        {{ Session::get('orderType') == 'sku' ? 'autofocus' : '' }}>
                    <!-- <div wire:loading wire:target="getPicklist">
                Processing picklist
            </div> -->
                </div>

                <div>
                    <p>SkuBlox</p>
                    <input data-hj-allow autocomplete="off" type="text" name="sortBox" id="sortBox"
                        wire:change="getOrder('sortBox', $event.target.value)"
                        class="p-2 mb-4 border h-12 w-64 text-3xl rounded"
                        {{ Session::get('orderType') == 'sortBox' ? 'autofocus' : '' }}>
                    <!-- <div wire:loading wire:target="sortBoxId">
                Processing skublox
            </div> -->
                </div>
            @else
                <livewire:picklist :picklist="$picklist" />

                <div>
                    <p>SKU</p>
                    <input data-hj-allow autocomplete="off" type="text" name="sku" id="sku"
                        wire:change="getOrder('sku', $event.target.value)"
                        class="p-2 mb-4 border h-12 w-64 text-3xl rounded"
                        {{ Session::get('orderType') == 'sku' ? 'autofocus' : '' }}>

                    <!-- <div wire:loading>
                Processing Payment...
            </div> -->
                </div>

                <div wire:click="resetPicklist">
                    <button class="btn btn-secondary btn-sm">Reset Picklist</button>
                </div>

            @endif
        </div>

    @elseif($simplifiedView && !$failed)
        <style>
            .modal {
                transition: opacity 0.25s ease;
            }

            body.big-modal-active {
                overflow-x: hidden;
                overflow-y: visible !important;
            }

        </style>
        <div class="modal fixed w-full h-full top-0 left-0 bg-white z-50">
            <div style="max-width:100vw;" class="w-full p-12">
                <div class="flex flex-col mb-8">
                    <div class="ml-auto">

                        <input type="password" name="pin" placeholder="PIN" id="pin" class="p-2 border h-8 w-16 rounded"  
                        maxlength="4" wire:model.defer="pin"></input>

                        <button wire:click="showDetails()" class="ml-4">
                           <span class="" style="color:#727986">Show Details</span> 
                        </button>

                        <button wire:click="resetOrder(true)" class="ml-4">
                            <span class="" style="color:#727986">Reset Order</span> 
                        </button>

                        @if ($confirmReset)
                            <div class="p-2 mt-4 bg-red-300 shadow overflow-hidden sm:rounded-lg">
                                <label for="resetOrder" class="m-2 text-gray-900">Please confirm you discarded the invoice.</br>
                                &nbsp; Confirme que descartó la factura. </label>

                                <input id="resetOrder" type="checkbox" name="resetOrder" class="outline-red"
                                    wire:click="resetOrder()" style="min-width:20px; min-height:20px;">
                            </div>
                        @endif

                    </div>
                </div>

                <div class="flex justify-center">
                    
                    <div class="text-3xl flex-1 flex justify-center items-center flex-col">
                        <p>
                            You shipped <span class="text-green-500 text-5xl" >{{$count}}</span> {{\Illuminate\Support\Str::plural('order', $count)}} today
                        </p>
                    </div>
                    <div style="min-width:376px;">
                    </div>
                </div>

                <div style="min-height:80vh;" class="flex justify-center">
                    <div class="flex-1 flex justify-center items-center flex-col">

                        <div class="" style="min-width: 600px;" >
                            @if (session()->has('message'))
                                <livewire:status :status="session('message')" :statusType="'message'" />
                            @endif
                           
                            @if (session()->has('error'))
                                <livewire:status :status="session('error')" :statusType="'error'" />
                            @endif
                       </div>

                       @if (array_key_exists('sortBoxId', $order) && $order['sortBoxId'] && array_key_exists('sortBoxCRC', $order) && $order['sortBoxCRC'])
                
                            <div class="flex mb-2 text-purple-600 cursor-pointer"
                                wire:click="flashLight({{ $order['sortBoxId'] }})">
                                <p class="text-2xl leading-6 font-medium cursor-pointer text-purple-600"
                                wire:click="flashLight({{ $order['sortBoxId'] }})">
                                Bin {{ $order['sortBoxCRC'] }}
                                </p>
                                <span class="ml-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 cursor-pointer"
                                        wire:click="flashLight({{ $order['sortBoxId'] }})" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                </span>
                            </div>
                    
                        @endif

                        @if (data_get($shippingStatistic, 'box_id') > 0)
                            <div class="mb-8" style="font-size:70px;">
                                Use {{App\Models\ShippingBox::find($shippingStatistic->box_id)->preset_name}} 
                            </div>
                        @else
                            <div class=" mt-6 mb-8">
                                <select wire:model="selectedShippingBox" name="selectedShippingBox"
                                    id="selectedShippingBox"
                                    class="mt-1 py-2 pl-3 pr-20 text-4xl text-left text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                                    style="font-size:40px;">
                                    <option class="text-left" value="-1">Choose a box</option>
                                    <option class="text-left" value="0">Use Custom box</option>
                                    @foreach ($shippingBoxes as $shippingBox)
                                        <option class="text-2xl" value="{{ $shippingBox['id'] }}">
                                            {{ $shippingBox['preset_name'] }}
                                            ({{ $shippingBox['width'] }}x{{ $shippingBox['height'] }}x{{ $shippingBox['depth'] }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            @if ($selectedShippingBox == 0)
                                <div class="mt-4  flex">
                                    <input data-hj-allow type="text" wire:model="order.OrderPackages.0.Width"
                                        placeholder="Width" class="p-2 mr-2 border h-12 w-52 text-3xl rounded">
                                    <input data-hj-allow type="text" wire:model="order.OrderPackages.0.Height"
                                        placeholder="Height" class="p-2 mr-2 border h-12 w-52 text-3xl rounded">
                                    <input data-hj-allow type="text" wire:model="order.OrderPackages.0.Length"
                                        placeholder="Length" class="p-2 mr-2 border h-12 w-52 text-3xl rounded">
                                </div>
                                
                            @endif
                        @endif

                        <div class="text-base mt-8 mb-4">  
                            Weight
                        </div>

                        <div class="mb-16">
                            @if ($order["ShippingDetails"]["EstShipWeight"]['Pounds'] == 0 && $order["ShippingDetails"]["EstShipWeight"]['Ounces'] == 0)
                                <div class="" id="lb" style="font-size:50px;">
                                    Place package & items on scale
                                </div>
                            @else
                                <div class="flex text-5xl">
                                    <div id="lb">
                                        {{$order['ShippingDetails']['EstShipWeight']['Pounds']}} LB &nbsp;
                                    </div>
                                    <div id="oz">
                                         {{$order['ShippingDetails']['EstShipWeight']['Ounces']}} OZ
                                    </div>
                                </div >
                            @endif
                        </div>

                        @php
                            $disabled = ($order["ShippingDetails"]["EstShipWeight"]['Pounds'] == 0 && $order["ShippingDetails"]["EstShipWeight"]['Ounces'] == 0) ||
                            !request()->cookie('label-printer') || 
                            // !session('labelPrinterOnline') ||
                            !$order['canShip'] ||
                            // $selectedShippingBox < 1 ||
                            $this->order['OrderPackages'][0]['Length'] < 1 ||
                            $this->order['OrderPackages'][0]['Width'] < 1 ||
                            $this->order['OrderPackages'][0]['Height'] < 1;

                            $text = [];
                            !request()->cookie('label-printer') ? $text += [0=> 'Printer Is Offline'] : null;
                            // !(request()->cookie('label-printer') || session('labelPrinterOnline')) ? $text += [0=> 'Printer Is Offline'] : null;
                            $this->order['OrderPackages'][0]['Length'] < 1 || $this->order['OrderPackages'][0]['Width'] < 1 || $this->order['OrderPackages'][0]['Height'] < 1 ? $text += [1 => 'No Box Selected'] : null;
                            !$order['canShip']  ? $text += [2 => 'Cant ship this order. Contact your manager'] : null;
                            !request()->cookie('scale') ? $text += [3 => 'Scale Is Offline'] : null;
                            
                            $sessionError = session('error') ;
                            $sessionMessage = session('message') ;
                        @endphp


                        @foreach ($text as $error)
                            <div class="text-xl mb-1 text-red-600"{{$loop->last ? 'style=margin-bottom:15px;' : ''}}>
                            {{$error}}
                            </div>
                        @endforeach
    

                        {{-- @if ($disabled)
                            <div class="text-xl mb-6 text-red-600">
                            {{ !request()->cookie('label-printer') || !session('labelPrinterOnline') ? 'Printer Is Offline' : ''}} 
                            {{ !$selectedShippingBox ? 'No Box Selected': ''}} 
                            {{!request()->cookie('scale') ? 'Scale Is Offline': ''}} 
                            {{ !$order['canShip'] ? 'Cant ship this order Contact your manager' : ''}}
                            </div>
                        @endif --}}

                        <div class="text-xl mb-6 text-red-600">
                            {{$sessionError}}
                            {{$sessionMessage}}
                        </div>

                        <div class="flex mb-3">
                            <div class="m-4" wire:loading.delay style="font-size: 49px;"> Now Close Box</div> 
                        </div>

                        <div class="">
                            <button class="btn p-20" style="padding:18px 70px; {{$disabled ? 'background-color: rgb(209 213 219);': ''}}"
                                wire:click="ratesAndShip()" wire:loading.delay.class="opacity-25" wire:loading.attr="disabled"
                                {{ $disabled ? 'disabled' : '' }}> 
                                <span style="font-size:29px;">Print & Ship</span>
                            </button>
                        </div>
                    </div>

                    <div style="max-width:376px;" class="w-full border rounded p-4 overflow-auto">

                        <div class="flex justify-between border-b mt-6 pb-4 mx-6">
                            <div class="text-sm font-bold">ITEMS</div>
                            <div class="text-sm font-bold">QTY</div>
                        </div>

                        @foreach ($order['OrderItems'] as $item)
                            <div class="flex justify-between border-b mt-6 pb-4 mx-6">
                                <div class="mr-4"> {{ $item['DisplayName'] }} </div>
                                <div class="{{ $item['Qty'] <= '1' ?: 'text-red-600' }}"> {{ $item['Qty'] }} </div>
                            </div>
                        @endforeach
                    
                    </div>

                </div>
            </div>
        </div>

        <script>
            const body = document.querySelector('body')
            body.classList.add('big-modal-active')

        </script>
    @else
        <script>
            const body = document.querySelector('body')
            body.classList.remove('big-modal-active')

        </script>

        <livewire:order :order="$order" />

        @if (collect(data_get($order, 'notes'))->count())
           <div>
                <div class="mb-4 inline-block">
                    <div class="text-sm font-medium text-gray-500 mb-2">
                        Actions
                    </div>
                    <label for="manualSignature" class="mr-1 ml-3 text-gray-900">Add Signature</label>
                    <input type="checkbox" name="manualSignature" id="manualSignature" class="outline-red"
                        wire:model="order.manualSignature">
                </div>
           </div>
        @endif
        
        
        @if (!$rates && isAdmin())
            <div class="mb-4 inline-block">
                <button wire:click="printInvoice(true)" wire:loading.class="opacity-50" wire:loading.attr="disabled"
                class=" pl-3 btn btn-sm ">Print Invoice</button>
            </div>
        @endif
        
        @if (!$rates && (!!$order['canShip'] || isAdmin()))
            @if (collect(data_get($order, 'gift_cards'))->count())
                <div class="mb-4 p-4 bg-orange-100 shadow overflow-hidden sm:rounded-lg">
                    <div class="sm:col-span-2 px-2">
                        <dt class="text-sm font-medium text-gray-500">
                            GIFT CARDS
                        </dt>
                        @foreach ($order['gift_cards'] as $gift_card)
                            <div class="flex items-center mt-1 mb-6">
                                <div class="pr-4">
                                    {{ $loop->iteration }}.
                                </div>

                                <button
                                    wire:click="printGiftCard({{ json_encode($gift_card) }},{{ $loop->index }})"
                                    wire:loading.class="opacity-50" wire:loading.attr="disabled"
                                    class=" pl-3 btn btn-sm    {{ (!array_key_exists('printed', $gift_card) || isAdmin()) ?: 'opacity-50' }}"
                                    {{ (!array_key_exists('printed', $gift_card) || isAdmin()) ?: 'disabled' }}>Print
                                    Gift
                                    Card</button>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
            @if (data_get($order, 'order_gift'))
                <div class="mb-4 p-4 bg-purple-100 shadow overflow-hidden sm:rounded-lg">
                    <div class="flex pb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                        </svg>
                        This order is a gift. &nbsp; &nbsp;- &nbsp; &nbsp; Este pedido es un regalo.
                    </div>
                    <div class=" m-3 p-4 border">
                        Wrap with {{ data_get($order, 'order_gift.gift_wrapping.name') }}
                        <div class="flex-shrink-0 flex h-24 w-24">
                            <img src="{{ data_get($order, 'order_gift.gift_wrapping.image') }}"
                                alt="{{ data_get($order, 'order_gift.gift_wrapping.description') }}"
                                style="object-fit: contain;">
                        </div>
                    </div>
                    @if (data_get($order, 'order_gift.gift_note'))
                        <button
                            wire:click="printGiftNote({{ json_encode(data_get($order, 'order_gift')) }},0,'order')"
                            wire:loading.class="opacity-50" wire:loading.attr="disabled" class=" pl-3 btn btn-sm">Print
                            Gift
                            Note</button>
                    @endif
                </div>
            @endif
            @if (data_get($order, 'items_gift'))
                <div class="mb-4 p-4 bg-purple-100 shadow overflow-hidden sm:rounded-lg">
                    <div class="flex pb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                        </svg>
                        Order Contains a gift &nbsp; &nbsp;- &nbsp; &nbsp; Pedido Contiene un regalo
                    </div>
                    @foreach ($order['items_gift'] as $gift)
                        @php
                            $item = collect($this->order['sc_item_Info'])
                                ->filter(function ($item) use ($gift) {
                                    return data_get($item, 'ID') == $gift['item_sku'];
                                })
                                ->values()
                                ->first();
                            
                            $index = collect($this->order['items_gift'])->search(function ($gift_item) use ($gift) {
                                return $gift_item['item_sku'] == $gift['item_sku'];
                            });
                        @endphp

                        <div
                            class=" pt-4 {{ !$loop->last ? 'mb-4 ' : '' }} p-4 bg-white shadow overflow-hidden sm:rounded-lg">
                            @if (!data_get($gift, 'done'))
                                <div>
                                    <div class="flex">
                                        <div class="flex-shrink-0 flex justify-center mr-8 h-20 w-20">
                                            <img src="{{ data_get($item, 'ImageUrl') ?? '/dummy-post-horisontal.jpeg' }}"
                                                alt="image" style="max-width:100%; max-height:100%;">
                                        </div>
                                        <div class="pr-2">
                                            <p class="text-sm font-medium text-gray-500">
                                                {{ data_get($item, 'ProductName') }}
                                            </p>
                                            <p class="text-xs font-medium text-gray-900 pt-1">
                                                SKU: {{ data_get($item, 'ID') }}
                                            </p>
                                            <p
                                                class="text-sm font-medium text-gray-900 pt-1 {{ $gift['item_quantity'] <= '1' ?: 'text-red-600' }}">
                                                Quantity:<span>
                                                    {{ $gift['item_quantity'] }}</span>
                                            </p>
                                        </div>
                                    </div>
                                    @if (data_get($gift, 'error') && !data_get($gift, 'confirmed'))
                                        <p class="text-red-700">
                                            {{ data_get($gift, 'error') }}
                                        </p>
                                    @endif
                                    @if (!data_get($gift, 'confirmed'))
                                        <div>
                                            <p class="pt-2"> Please scan UPC</p>
                                            {{-- @if (data_get($gift, 'error'))
                                            <p class="text-red-700">
                                                {{ data_get($gift, 'error') }}
                                            </p>
                                        @endif --}}
                                            <input type="text" name="scan" id="scan" placeholder="UPC"
                                                wire:change="confirmUpc({{ json_encode($gift) }}, $event.target.value)"
                                                class="p-2 mb-4 border h-12 w-64 text-3xl rounded">
                                        </div>
                                    @endif
                                </div>
                                @if (data_get($gift, 'confirmed'))
                                    {{-- todo display wrapping info and button to print note --}}
                                    <div class=" m-3 p-4 border">
                                        Wrap with {{ data_get($gift, 'gift_wrapping.name') }}
                                        <div class="flex-shrink-0 flex h-24 w-24">
                                            <img src="{{ data_get($gift, 'gift_wrapping.image') }}"
                                                alt="{{ data_get($gift, 'gift_wrapping.description') }}"
                                                style="object-fit: contain;">
                                        </div>
                                    </div>
                                    @if (data_get($gift, 'gift_note'))
                                        <button
                                            wire:click="printGiftNote({{ json_encode($gift) }},{{ $loop->index }},'item')"
                                            wire:loading.class="opacity-50" wire:loading.attr="disabled"
                                            class=" pl-3 btn btn-sm">Print
                                            Gift
                                            Note</button>
                                    @endif
                                @endif
                            @endif
                            @if (collect($order['items_gift'])->count() > 1)                          
                                 <div class="flex items-center">
                                    <label for="items_gift{{ $loop->index }}" class="mr-3 text-gray-900">
                                        Completed wrapping {{ data_get($gift, 'item_sku') }}
                                    </label>
                                    <input type="checkbox" name="done" id="items_gift{{ $loop->index }}"
                                    class="outline-red" wire:model="order.items_gift.{{ $loop->index }}.done"
                                    style="min-width:20px; min-height:20px;">
                                </div>
                            @endif
                            {{-- todo display wrapping info and button to print note --}}
                        </div>
                    @endforeach
                </div>
            @endif

        @endif

        <div>
            <div class="mb-4 inline-block">
                @if ($confirmReset)
                    <div class="flex items-center p-2 mt-1 mb-2 bg-red-300 shadow overflow-hidden sm:rounded-lg">
                        <label for="resetOrder" class="mr-3 text-gray-900"> Please confirm you discarded the invoice.
                            ,Confirme que descartó la factura. </label>
                        <input id="resetOrder" type="checkbox" name="resetOrder" class="outline-red"
                            wire:click="resetOrder()" style="min-width:20px; min-height:20px;">
                    </div>
                @endif
                <button wire:click="resetOrder(true)" class="btn btn-secondary btn-sm">Reset Order</button>
            </div>
        </div>

        @if ($needsService == 'mapping')
            <p>This method needs mapping</p>
            <table class="min-w-full divide-y divide-gray-200">
                <livewire:tablehead :columns="['ID', 'SellerCloud Name', 'Map To', 'Mapping']" />
                <tbody wire:poll="changeServiceMapping" class="bg-white divide-y divide-gray-200">
                    <livewire:sc-service :serviceName="$this->order['ShippingDetails']['Service']"
                        :seServices="$seServices" :serviceMappings="$serviceMappings" />
                </tbody>
            </table>
                            {{-- <div>
                                Sellercloud Service: {{ $this->order['ShippingDetails']['Service'] }}

                    <select onfocus="this.blur();" name="service" id="service" wire:change="changeServiceMapping($event.target.value)" class="mt-1 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                        <option>Select</option>
                        @foreach ($seServices as $service)
                        <option value="{{ $service['id'] }}">{{ $service['name'] }} - {{ $service['service_code'] }}</option>
                        @endforeach
                    </select>
                </div> --}}
        @elseif($needsService == 'speed')
            <div>
                ShipEngine Service: {{ $service['se_name'] }} ({{ $service['service_code'] }})

                <select onfocus="this.blur();" name="serviceSpeed" id="serviceSpeed"
                    wire:change="changeServiceSpeed($event.target.value)"
                    class="mt-1 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    <option>Select</option>
                    @foreach ($serviceMappings as $mapping)
                        <option value="{{ $mapping['id'] }}">{{ $mapping['name'] }}</option>
                    @endforeach
                </select>
            </div>
        @elseif(isset($service))
            <div class="border rounded px-4 py-1 mb-2">
                Sellercloud Service: <b>{{ $order['ShippingDetails']['Service'] }}</b><br />
                Group: <b>{{ $service['name'] }}</b><br />

                @if ($service['service_id'])
                    ShipEngine Service: <b>{{ $service['service_code'] }}</b><br />
                @endif
            </div>
        @endif

                    {{-- <section class="my-6">
                        <select wire:model="selectedCarriers" wire:change="getPackageTypes()" name="carriers" id="carriers" multiple>
                            @foreach ($carriers as $carrier)
                                <option value="{{ $carrier['carrier_id'] }}">{{ $carrier['friendly_name'] }}</option>
            @endforeach
            </select>
            </section>

            <section class="my-6">
                <select wire:model="selectedPackageTypes" name="packageTypes" id="packageTypes" multiple>
                    <option selected value>--Preset packaging--</option>
                    @foreach ($packageTypes as $packageType)
                    <option value="{{ $packageType['package_code'] }}">{{ $packageType['name'] }}</option>
                    @endforeach
                </select>
            </section> --}}

        @if ($rates && is_array($rates) && count($rates))
            <div>
                @if (array_key_exists('requiresSignature', $order) && $order['requiresSignature'])
                    <p>These rates will require a signature</p>
                @endif

                <select onfocus="this.blur();" name="selectedRate" id="selectedRate" wire:model="selectedRate"
                    class="mt-1 form-select pl-3 pr-10 py-2 text-base leading-6 border-gray-300 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 sm:text-sm sm:leading-5">
                    @foreach ($rates as $rate)
                        <option value="{{ $rate['rate_id'] }}" @if (isset($rate['selected']))  @endif>{{ $rate['carrier_friendly_name'] }} @if ($rate['carrier_nickname']) ({{ $rate['carrier_nickname'] }})
                            @endif - {{ $rate['service_type'] }}
                            (${{ $rate['shipping_amount']['amount'] + $rate['insurance_amount']['amount'] + $rate['confirmation_amount']['amount'] + $rate['other_amount']['amount'] }})
                        </option>
                        {{-- <livewire:rate :rate="$rate" :key="$rate['rate_id']"> --}}
                    @endforeach
                </select>
                <button class="btn btn-secondary btn-sm" wire:click="resetRate()">Reset Rates</button>

                <div>
                    <button class="btn mt-2 {{ request()->cookie('label-printer') ? '' : 'opacity-50' }}"
                        wire:click="ship()" wire:loading.class="opacity-50" wire:loading.attr="disabled"
                        {{ request()->cookie('label-printer') ? '' : 'disabled' }}>Ship</button>
                </div>
            </div>

            @if ($rateReason != '')
                The selected rate was chosen because {{ $rateReason }}
            @endif

            @if (array_key_exists('triedShipping', $order) && $order['triedShipping'])
                @if (!$label)
                    <div>We encountered an issue creating a label</div>
                @endif
                @if (!array_key_exists('shipment', $order) || !$order['shipment'])
                    <div>We encountered an issue saving the shipment to the database</div>
                @endif

                @if (!array_key_exists('printJob', $order) || !$order['printJob'])
                    <div>We encountered an issue printing the label</div>
                @endif

                @if (!array_key_exists('scResponse', $order) || !$order['scResponse'])
                    <div>We encountered an issue sending the order to sellercloud</div>
                @endif

                @if (!array_key_exists('emptySortbox', $order) || !$order['emptySortbox'])
                    <div>We encountered an issue turning off the light</div>
                @endif

                <button class="btn btn-secondary btn-sm" wire:click="ship">Try again</button>
            @endif
        @elseif(array_key_exists('canShip', $order) && $order['canShip'])
            <form autocomplete="off" action="#" wire:submit.prevent="getRates()">
                <div class="flex">
                    @if (empty($selectedPackageType))
                        <div class="mt-6">
                            <div>
                                <p>Dimensions</p>
                                <div>
                                    <select wire:model="selectedShippingBox" name="selectedShippingBox"
                                        id="selectedShippingBox"
                                        class="mt-1 pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                        <option value="0" >Use Custom box</option>
                                        @foreach ($shippingBoxes as $shippingBox)
                                            <option value="{{ $shippingBox['id'] }}">
                                                {{ $shippingBox['preset_name'] }}
                                                ({{ $shippingBox['width'] }}x{{ $shippingBox['height'] }}x{{ $shippingBox['depth'] }})
                                                @if (optional($this->shippingStatistic)->box_id == $shippingBox['id'])
                                                    - RECOMMENDED @endif
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mt-4  flex">
                                    <input data-hj-allow type="text" wire:model="order.OrderPackages.0.Width"
                                        placeholder="Width" class="p-2 mr-2 border h-12 w-52 text-3xl rounded" {{ $selectedShippingBox > 0 ? 'disabled' : '' }}>
                                    <input data-hj-allow type="text" wire:model="order.OrderPackages.0.Height"
                                        placeholder="Height" class="p-2 mr-2 border h-12 w-52 text-3xl rounded" {{ $selectedShippingBox > 0 ? 'disabled' : '' }}>
                                    <input data-hj-allow type="text" wire:model="order.OrderPackages.0.Length"
                                        placeholder="Length" class="p-2 mr-2 border h-12 w-52 text-3xl rounded" {{ $selectedShippingBox > 0 ? 'disabled' : '' }}>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div>
                        <div>
                            <p class="ml-6 mt-12">Weight</p>
                        </div>
                        <div class="flex ml-4 mt-8">
                            <input data-hj-allow type="text"
                                wire:model.lazy="order.ShippingDetails.EstShipWeight.Pounds" id="lb" name="lb"
                                placeholder="LB" class="p-2 mr-2 border h-12 w-64 text-3xl rounded" disabled>
                            <input data-hj-allow type="text"
                                wire:model.lazy="order.ShippingDetails.EstShipWeight.Ounces" id="oz" name="oz"
                                placeholder="OZ" class="p-2 mr-2 border h-12 w-64 text-3xl rounded" disabled>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn mt-12">
                        Get rates
                    </button>
                </div>
            </form>
            @if ($rates == 'no_rate')
                No valid rates were found
            @endif
        @elseif(array_key_exists('previous_orders', $order) && count($order['previous_orders']))
            <p class="mt-8">This order was previously shipped</p>
            <table>
                <tbody>
                    @foreach ($order['previous_orders'] as $shipment)
                        <livewire:shipment :shipment="$shipment" :key="$shipment['id']">
                    @endforeach
                </tbody>
            </table>
        @endif

    @endif

</div>

@if (!auth()->user()->useQZ)
    {{-- PrintNode --}}
    <script src="/js/printnode-ws-http-client.js"></script>
    <script>
        document.addEventListener('livewire:load', function() {
            let options = {
                apiKey: '{{ $printNodeApiKey }}'
            }

            function authenticate(authData) {
                if (authData.error) {
                    console.log(authData.error);
                    return;
                }

                // ok, we're authenticated - now let's get some scales data
                var scalesSub = this.getScales({
                    // computerId: '{{ $user->ComputerId }}',
                    // productId: '{{ $user->ScaleId }}'
                    computerId: "{{ request()->cookie('computer') }}",
                    productId: "{{ request()->cookie('scale') }}"
                }, function subCallback(scalesMeasurement) {
                    if (document.getElementById('lb')) {
                        @this.updateWeight(scalesMeasurement.measurement.lb / 1000000000);
                        if (scalesMeasurement.measurement.lb) {
                            let weight = scalesMeasurement.measurement.lb / 1000000000;
                            let lb = Math.floor(weight);
                            let oz = Math.max(0, (weight - lb) * 16);
                            document.getElementById('lb').value = lb;
                            document.getElementById('lb').dispatchEvent(new Event('change'));
                            document.getElementById('oz').value = oz.toFixed(1);
                            document.getElementById('oz').dispatchEvent(new Event('change'));
                            console.log(weight, lb, oz, oz.toFixed(1));
                        }
                    }
                });
            }

            function error(err) {
                console.error(err);
            }

            var ws = new PrintNode.WebSocket(options, authenticate, error);
            ws.subscribe("authenticate", authenticate);
            ws.subscribe("error", error);

            Livewire.on('focus-on-field', field => {
                console.log(field);
                if (!field) {
                    field = 'sortBox'
                }

                window.setTimeout(function() {
                    document.getElementById(field).focus();
                }, 0);
            })
        });

    </script>
@else
    <script>
    </script>
@endif
<script>
console.log('test');
const pin = document.getElementById("pin");

pin.type = 'text';

pin.addEventListener("keydown", (event) => {
    pin.type = 'password';
});
</script>
<style>
    .loader {
        color: #76a9fa;
        font-size: 6px;
        margin: 20px 10px;
        width: 1em;
        height: 1em;
        border-radius: 50%;
        position: relative;
        text-indent: -9999em;
        -webkit-animation: load4 1.3s infinite linear;
        animation: load4 1.3s infinite linear;
        -webkit-transform: translateZ(0);
        -ms-transform: translateZ(0);
        transform: translateZ(0);
    }

    @-webkit-keyframes load4 {

        0%,
        100% {
            box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
        }

        12.5% {
            box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
        }

        25% {
            box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
        }

        37.5% {
            box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
        }

        50% {
            box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
        }

        62.5% {
            box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
        }

        75% {
            box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
        }

        87.5% {
            box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
        }
    }

    @keyframes load4 {

        0%,
        100% {
            box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
        }

        12.5% {
            box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
        }

        25% {
            box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
        }

        37.5% {
            box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
        }

        50% {
            box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
        }

        62.5% {
            box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
        }

        75% {
            box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
        }

        87.5% {
            box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
        }
    }

</style>
