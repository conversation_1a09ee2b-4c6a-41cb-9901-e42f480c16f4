<tr>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="flex items-center">
            @if ($shipment->user)
                <div
                    class="flex items-center justify-center flex-shrink-0 h-10 w-10 rounded-full text-white bg-blue-500">
                    <span>{{ $shipment->user['initials'] }}</span>
                </div>
            @endif
            <div class="ml-4">
                <div class="text-sm leading-5 font-medium text-gray-900">
                    <a target="_blank"
                        href="https://pfl.cwa.sellercloud.com/orders/Orders_details.aspx?ID={{ $shipment->order_id }}">Order
                        ID: {{ $shipment->order_id }}</a>
                    <p class="text-gray-500">Skublox ID: {{ data_get($shipment, 'sortbox_crc') }}</p>
                </div>
            </div>
        </div>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{$shipment->carrier}} - {{ $shipment->service_code }} -
            ${{ $shipment->shipment_cost }}</div>
        <div class="text-sm leading-5 text-gray-500">
            Shipping Dims: {{ $shipment->length }} X {{ $shipment->height }} X {{ $shipment->width }} Weight: {{floor($shipment->weight / 16)}}lb {{fmod($shipment->weight, 16)}} oz
        </div>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">
            {{ date('M j, Y \a\t g:i a', strtotime($shipment->se_create_date)) }}</div>
        @if ($shipment->user)
            <div class="text-sm leading-5 text-gray-500">{{ $shipment->user['email'] }}</div>
        @endif
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        @switch($shipment->status)
            @case('voided')
            @case('error')
                @php
                    $color = 'red';
                @endphp
            @break
        @case('processing')
            @php
                $color = 'yellow';
            @endphp
        @break
        @default
            @php
                $color = 'green';
            @endphp

    @endswitch
    <span
        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{ $color }}-100 text-{{ $color }}-800">
        {{ $shipment->status }}
    </span>
</td>
<td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
<div> <a target="_blank" href="https://www.google.com/search?q={{ $shipment->tracking_number }}">
        {{ $shipment->tracking_number }} </a> </div>
</td>
<td class="px-6 py-4 whitespace-no-wrap text-right text-sm leading-5 font-medium">
@if ($shipment->status == 'completed' && isAdmin())
    <button
        wire:click="unship('{{ $shipment->label_id }}', '{{ $shipment->order_id }}','{{ $shipment->id }}')"
        class="btn btn-secondary btn-sm">Unship</button>

    @if (auth()->user()->useQZ)
        <button wire:mousedown="unship('{{ $shipment->label_id }}', '{{ $shipment->order_id }}')"
            wire:loading.class="opacity-50" wire:loading.attr="disabled" class="btn btn-secondary btn-sm">
            Unship
        </button>
    @endif

    @if (auth()->user()->useQZ)
        <button onclick="QZPrint('{{ $shipment->zpl }}')" class="btn btn-sm">Reprint</button>
    @else
        <button
            wire:mousedown="print('{{ str_replace('https', 'http', $shipment->pdf) }}', 'reprint: Order ID: {{ $shipment->order_id }}, Label ID: {{ $shipment->label_id }}')"
            wire:loading.class="opacity-50" wire:loading.attr="disabled" class="btn btn-sm">
            Reprint
        </button>
    @endif
@endif
</td>
</tr>
