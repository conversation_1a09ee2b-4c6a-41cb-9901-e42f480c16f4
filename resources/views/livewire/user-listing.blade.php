<tr>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="flex items-center">
            <div class="flex items-center justify-center flex-shrink-0 h-10 w-10 rounded-full text-white bg-blue-500">
                <span>{{ $user['initials'] }}</span>
            </div>
            <div class="ml-4">
                <div class="text-sm leading-5 font-medium text-gray-900">
                    {{ $user->name }}
                </div>
            </div>
        </div>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{ $user->email }}</div>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        <div class="text-sm leading-5 text-gray-900 font-medium">{{ date('M j, Y \a\t g:i a', strtotime($user->created_at)) }}</div>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap cursor-pointer" wire:click="changeStatus()">
        @switch($user->status)
        @case('inactive')
        @php
        $color = 'red';
        @endphp
        @break
        @default
        @php
        $color = 'green';
        @endphp

        @endswitch
        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{$color}}-100 text-{{$color}}-800">
            {{ $user->status }}
        </span>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap">
        <input style="background-color: #3f83f8;background-color: rgba(63, 131, 248, var(--bg-opacity)); pointer-events:none;" class="ml-3" type="checkbox" {{!$user->receive_notifications?:"checked"}}>
    </td>
    <td class="flex items-center px-6 py-4 whitespace-no-wrap">
        <input class="p-2 pr-24 border h-10 text-3xl rounded focus:ring-indigo-500 focus:border-indigo-500" type="text" name="rawPass" wire:model="rawPassword">
        <button wire:click="updatePassword()" class="btn btn-sm h-8 -ml-20">Save</button>
    </td>
    <td class="px-6 py-4 whitespace-no-wrap text-right text-sm leading-5 font-medium">
        <button class="btn btn-sm btn-secondary"><a href="/users/{{$user->id}}">Edit</a></button>
    </td>
</tr>