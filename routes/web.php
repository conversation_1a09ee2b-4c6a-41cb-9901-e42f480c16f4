<?php

use App\Http\Controllers\TestController;
use App\Library\Services\PrintNodeAPI;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;

// auth()->loginUsingId(1);

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/uptime', function () {
    return 'hello';
});

Route::middleware('auth')->group(function () {
    Route::get('/', function () {
        return view('pages.home');
    });

    Route::get('/manifests', function () {
        return view('pages.manifests');
    });

    Route::get('/shipments', function () {
        return view('pages.shipments');
    });

    Route::get('/user', function () {
        return view('pages.user');
    });
    Route::get('/user-info', function () {

        $printNodeApi = new PrintNodeAPI;

        $scales = $printNodeApi->getScales(request()->cookie('computer'));

        $computer = $printNodeApi->getComputer(request()->cookie('computer'));

        $cardPrinter = $printNodeApi->getPrinter(request()->cookie('card-printer'));

        $labelPrinter = $printNodeApi->getPrinter(request()->cookie('label-printer'));
        
        $invoicePrinter = $printNodeApi->getPrinter(request()->cookie('invoice-printer'));
        session()->put('cardPrinterOnline', data_get($cardPrinter,'0.state') === 'online');
        session()->put('labelPrinterOnline', data_get($labelPrinter,'0.state') === 'online');
        session()->put('invoicePrinterOnline', data_get($invoicePrinter,'0.state') === 'online');
        return [
            'card_printer' => $cardPrinter,
            'label_printer' => $labelPrinter,
            'invoice_printer' => $invoicePrinter,
            // 'printer' => auth()->user()->useQZ ? [['name' => 'QZ Tray']] : $printer,
            'scales' => $scales,
            'computer' => $computer,
            // 'time' => time()
        ];
    });
});

Route::middleware(['auth', 'role'])->group(function () {
    Route::get('/test', function () {
        return (new \App\Http\Controllers\TestController)->index();
        // session()->forget('sc_token');
        // logOrder(123, 'hello');

        // session()->forget('ship_order');
        // auth()->logout();;
    });

    Route::get('/settings', function () {
        return view('pages.settings');
    });

    Route::get('/users', function () {
        return view('pages.users');
    });

    Route::get('/users/new', function () {
        return view('pages.user', ['userId' => 'new']);
    });

    Route::get('/users/{userId}', function ($userId) {
        return view('pages.user', ['userId' => $userId]);
    })->whereNumber('userId');

    Route::get('/issues', function () {
        return view('pages.issues');
    });

    Route::get('/presets', function () {
        return view('pages.presets');
    });

    Route::get('/presets/new', function () {
        return view('pages.presets-new', ['shippingStatisticId' => 'new']);
    });

    Route::get('/presets/{shippingStatisticId}', function ($shippingStatisticId) {
        return view('pages.presets-new', ['shippingStatisticId' => $shippingStatisticId]);
    })->whereNumber('shippingStatisticId');

    Route::get('/test/{user}',  [TestController::class, 'index']);
});

Auth::routes(['register' => FALSE]);

// Route::get('/reset', function () {
//     // Cache::flush(); // ()->forget();
//     // logOrder(123, 'hello');

//     // auth()->logout();
// });
